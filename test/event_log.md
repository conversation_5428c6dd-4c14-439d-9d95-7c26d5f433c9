# AppsFlyer 事件上报总结

本项目在 `lib/services/event_log.dart` 中对主要业务事件进行了 AppsFlyer 上报，具体如下：

| 事件方法                | AppsFlyer 事件名      | 上报参数（示例）                                   | 说明                         |
|------------------------|----------------------|---------------------------------------------------|------------------------------|
| logScreenView          | screen_view          | screen_name, timestamp                            | 屏幕访问                     |
| logWebsiteVisit        | website_visit        | url, domain, method, timestamp                    | 网站访问                     |
| logLogin               | af_login             | af_login_method, timestamp                        | 用户登录（已实现）           |
| logSignUp              | af_complete_registration | af_registration_method, timestamp            | 用户注册（已实现）           |
| logContentView         | af_content_view      | af_content_type, af_content_id, af_content, timestamp | 内容查看（已实现）       |
| logSearch              | af_search            | af_search_string, timestamp                       | 搜索（已实现）               |
| logShare               | af_share             | af_content_type, af_content_id, af_channel, timestamp | 分享（已实现）           |
| logEnterPurchase       | enter_purchase       | user_id, timestamp                                | 进入购买页                   |
| logPurchase            | af_purchase          | af_revenue, af_currency, af_content_id, af_content, timestamp | 购买（已实现）      |
| logImageTranslation    | image_translation    | product_id, target_lang, success, error_message, timestamp | 图片翻译               |
| logMangaDownload       | manga_download       | success_count, timestamp                          | 漫画下载                     |
| logFeedback            | user_feedback        | feedback_type, content, timestamp                 | 用户反馈                     |
| logCustomEvent         | eventName(自定义)    | 传入参数全部转字符串，timestamp                    | 自定义事件                   |
| logPhotoCapture        | photo_capture        | success, timestamp                                | 拍照                         |
| logPhotoUpload         | photo_upload         | count, timestamp                                  | 相册图片上传/选择             |
| logTranslateStart      | translate_start      | target_lang, timestamp                            | 翻译开始                     |

## 说明
- 所有事件均调用 `AppsFlyerService.logEvent` 进行上报，事件名与参数与 Firebase Analytics 保持一致。
- 所有参数均转为字符串类型，符合 AppsFlyer 要求。
- 事件方法均在 `EventLogService` 静态方法体内直接调用 AppsFlyer 上报。
- 事件参数中均包含 `timestamp` 字段，记录事件发生时间。
- 事件注释、参数、命名均遵循 Dart/Flutter 规范与本项目风格。

## 代码位置
- 事件上报代码均位于 `lib/services/event_log.dart`，AppsFlyer 相关逻辑调用 `lib/services/appsflyer_service.dart`。

如需扩展新事件，直接在对应事件方法内补充 AppsFlyer 上报即可。 