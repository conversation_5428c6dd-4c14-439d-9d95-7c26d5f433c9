import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:imtrans/services/webview_js_resources.dart';

void main() {
  group('WebViewJsResources Tests', () {
    setUpAll(() async {
      // 设置测试环境
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('should have available resources', () {
      final resources = WebViewJsResources.getAvailableResources();
      expect(resources, isNotEmpty);
      expect(resources, contains('translation_overlay'));
      expect(resources, contains('browser_utils'));
      expect(resources, contains('ad_block'));
      expect(resources, contains('content_extractor'));
    });

    test('should check resource existence', () {
      expect(WebViewJsResources.hasResource('translation_overlay'), isTrue);
      expect(WebViewJsResources.hasResource('nonexistent'), isFalse);
    });

    test('should get resource path', () {
      final path = WebViewJsResources.getResourcePath('translation_overlay');
      expect(path, equals('assets/js/translation_overlay.js'));
    });

    test('should get resource dependencies', () {
      final deps = WebViewJsResources.getResourceDependencies('translation_overlay');
      expect(deps, contains('browser_utils'));
    });

    test('should get cache stats', () {
      final stats = WebViewJsResources.getCacheStats();
      expect(stats, contains('cachedResources'));
      expect(stats, contains('maxCacheSize'));
      expect(stats, contains('cacheExpiration'));
    });

    test('should clear cache', () {
      WebViewJsResources.clearAllCache();
      final stats = WebViewJsResources.getCacheStats();
      expect(stats['cachedResources'], equals(0));
    });

    test('should add and remove custom resource path', () {
      WebViewJsResources.addResourcePath('test_resource', 'assets/js/test.js');
      expect(WebViewJsResources.hasResource('test_resource'), isTrue);
      
      WebViewJsResources.removeResourcePath('test_resource');
      expect(WebViewJsResources.hasResource('test_resource'), isFalse);
    });

    test('should load translation_overlay.js content', () async {
      final content = await WebViewJsResources.getContent('translation_overlay');
      expect(content, isNotEmpty);
      expect(content, contains('window.translationOverlays'));
      expect(content, contains('initialize: function()'));
      expect(content, contains('initIntersectionObserver: function()'));
      expect(content, contains('setupScrollHandler: function()'));
    });
  });
} 