import 'package:flutter_test/flutter_test.dart';
import 'package:imtrans/services/webview_injection_service.dart';

void main() {
  group('WebViewInjectionService Tests', () {
    late WebViewInjectionService injectionService;

    setUp(() {
      injectionService = WebViewInjectionService();
      injectionService.clearInjectionHistory();
    });

    test('should create singleton instance', () {
      final instance1 = WebViewInjectionService();
      final instance2 = WebViewInjectionService();
      expect(identical(instance1, instance2), isTrue);
    });

    test('should clear injection history', () {
      injectionService.clearInjectionHistory();
      expect(injectionService.getInjectionHistory(), isEmpty);
    });

    test('should track injection history', () {
      expect(injectionService.isInjected('test_resource'), isFalse);
      
      // 模拟注入记录
      injectionService.removeInjectionRecord('test_resource');
      expect(injectionService.isInjected('test_resource'), isFalse);
    });

    test('should have correct placeholder format', () {
      // 测试占位符格式是否正确
      const cssPlaceholder = '__STYLE_ID_PLACEHOLDER__';
      const jsPlaceholder = '__NAMESPACE_PLACEHOLDER__';
      const jsContentPlaceholder = '__JS_CONTENT_PLACEHOLDER__';
      const cssContentPlaceholder = '__CSS_CONTENT_PLACEHOLDER__';
      
      expect(cssPlaceholder, contains('PLACEHOLDER'));
      expect(jsPlaceholder, contains('PLACEHOLDER'));
      expect(jsContentPlaceholder, contains('PLACEHOLDER'));
      expect(cssContentPlaceholder, contains('PLACEHOLDER'));
    });

    test('should handle translationOverlays namespace specially', () {
      // 测试 translationOverlays 命名空间是否被特殊处理
      const namespace = 'translationOverlays';
      expect(namespace, equals('translationOverlays'));
    });
  });
} 