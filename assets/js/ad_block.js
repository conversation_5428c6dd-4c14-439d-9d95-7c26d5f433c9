// 广告拦截功能
window.adBlocker = {
  // 广告选择器列表
  adSelectors: [
    '[class*="ad"]',
    '[class*="ads"]',
    '[class*="advertisement"]',
    '[id*="ad"]',
    '[id*="ads"]',
    '[id*="advertisement"]',
    'iframe[src*="doubleclick"]',
    'iframe[src*="googlesyndication"]',
    'iframe[src*="ad"]',
    '[class*="banner"]',
    '[id*="banner"]',
    '[class*="sponsor"]',
    '[id*="sponsor"]',
    '[class*="popup"]',
    '[id*="popup"]'
  ],
  
  // 初始化广告拦截
  init: function() {
    this.hideAds();
    this.observeNewAds();
    console.log('Ad blocker initialized');
  },
  
  // 隐藏现有广告
  hideAds: function() {
    this.adSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        this.hideElement(element);
      });
    });
  },
  
  // 观察新添加的广告
  observeNewAds: function() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            this.checkAndHideAds(node);
          }
        });
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  },
  
  // 检查并隐藏广告
  checkAndHideAds: function(element) {
    this.adSelectors.forEach(selector => {
      if (element.matches && element.matches(selector)) {
        this.hideElement(element);
      }
      
      const children = element.querySelectorAll(selector);
      children.forEach(child => {
        this.hideElement(child);
      });
    });
  },
  
  // 隐藏元素
  hideElement: function(element) {
    if (element && !element.dataset.adHidden) {
      element.style.display = 'none';
      element.dataset.adHidden = 'true';
    }
  },

  // 显示被隐藏的元素
  showElement: function(element) {
    if (element && element.dataset.adHidden) {
      element.style.display = '';
      delete element.dataset.adHidden;
    }
  },

  // 获取隐藏的广告元素数量
  getHiddenAdsCount: function() {
    return document.querySelectorAll('[data-ad-hidden="true"]').length;
  },

  // 获取所有隐藏的广告元素
  getHiddenAds: function() {
    return Array.from(document.querySelectorAll('[data-ad-hidden="true"]'));
  },

  // 恢复所有被隐藏的广告
  restoreAllAds: function() {
    const hiddenAds = this.getHiddenAds();
    hiddenAds.forEach(ad => {
      this.showElement(ad);
    });
    console.log('Restored', hiddenAds.length, 'ads');
  },

  // 添加自定义广告选择器
  addAdSelector: function(selector) {
    if (!this.adSelectors.includes(selector)) {
      this.adSelectors.push(selector);
      // 立即应用新选择器
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        this.hideElement(element);
      });
    }
  },

  // 移除广告选择器
  removeAdSelector: function(selector) {
    const index = this.adSelectors.indexOf(selector);
    if (index > -1) {
      this.adSelectors.splice(index, 1);
      // 恢复被该选择器隐藏的元素
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element.dataset.adHidden) {
          this.showElement(element);
        }
      });
    }
  },

  // 获取当前广告选择器列表
  getAdSelectors: function() {
    return [...this.adSelectors];
  },

  // 检查元素是否为广告
  isAd: function(element) {
    return this.adSelectors.some(selector => {
      try {
        return element.matches(selector);
      } catch (e) {
        return false;
      }
    });
  },

  // 获取页面上的广告元素（未隐藏的）
  getVisibleAds: function() {
    const ads = [];
    this.adSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (!element.dataset.adHidden) {
          ads.push(element);
        }
      });
    });
    return ads;
  },

  // 统计页面广告信息
  getAdStats: function() {
    const visibleAds = this.getVisibleAds();
    const hiddenAds = this.getHiddenAds();
    
    return {
      visibleCount: visibleAds.length,
      hiddenCount: hiddenAds.length,
      totalCount: visibleAds.length + hiddenAds.length,
      selectorsCount: this.adSelectors.length
    };
  }
}; 