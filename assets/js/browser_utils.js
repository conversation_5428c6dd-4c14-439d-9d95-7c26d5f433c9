// 浏览器通用工具库
window.browserUtils = {
  // 检测浏览器环境
  isMobile: function() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },
  
  // 获取页面信息
  getPageInfo: function() {
    return {
      title: document.title,
      url: window.location.href,
      domain: window.location.hostname,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  },
  
  // 安全的 DOM 操作
  safeQuerySelector: function(selector) {
    try {
      return document.querySelector(selector);
    } catch (e) {
      console.warn('Invalid selector:', selector);
      return null;
    }
  },
  
  // 防抖函数
  debounce: function(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },
  
  // 节流函数
  throttle: function(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // 检查元素是否在视口中
  isElementInViewport: function(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  },

  // 滚动到元素
  scrollToElement: function(element, options = {}) {
    const defaultOptions = {
      behavior: 'smooth',
      block: 'center',
      inline: 'center'
    };
    const finalOptions = { ...defaultOptions, ...options };
    element.scrollIntoView(finalOptions);
  },

  // 获取元素相对于视口的位置
  getElementPosition: function(element) {
    const rect = element.getBoundingClientRect();
    return {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width,
      height: rect.height
    };
  },

  // 监听元素大小变化
  observeElementSize: function(element, callback) {
    if (window.ResizeObserver) {
      const observer = new ResizeObserver(entries => {
        entries.forEach(entry => {
          callback(entry.contentRect);
        });
      });
      observer.observe(element);
      return observer;
    } else {
      // 降级方案
      let lastWidth = element.offsetWidth;
      let lastHeight = element.offsetHeight;
      
      const checkSize = () => {
        const currentWidth = element.offsetWidth;
        const currentHeight = element.offsetHeight;
        
        if (currentWidth !== lastWidth || currentHeight !== lastHeight) {
          lastWidth = currentWidth;
          lastHeight = currentHeight;
          callback({ width: currentWidth, height: currentHeight });
        }
      };
      
      const interval = setInterval(checkSize, 100);
      return { disconnect: () => clearInterval(interval) };
    }
  },

  // 获取页面滚动位置
  getScrollPosition: function() {
    return {
      x: window.scrollX || window.pageXOffset,
      y: window.scrollY || window.pageYOffset
    };
  },

  // 设置页面滚动位置
  setScrollPosition: function(x, y, smooth = true) {
    if (smooth) {
      window.scrollTo({
        left: x,
        top: y,
        behavior: 'smooth'
      });
    } else {
      window.scrollTo(x, y);
    }
  },

  // 监听页面滚动
  onScroll: function(callback, options = {}) {
    const defaultOptions = { passive: true };
    const finalOptions = { ...defaultOptions, ...options };
    
    const throttledCallback = this.throttle(callback, options.throttle || 16);
    window.addEventListener('scroll', throttledCallback, finalOptions);
    
    return {
      remove: () => window.removeEventListener('scroll', throttledCallback)
    };
  },

  // 获取页面加载状态
  getPageLoadState: function() {
    return {
      readyState: document.readyState,
      isComplete: document.readyState === 'complete',
      isInteractive: document.readyState === 'interactive',
      isLoading: document.readyState === 'loading'
    };
  },

  // 等待页面加载完成
  waitForPageLoad: function() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve, { once: true });
      }
    });
  },

  // 等待 DOM 内容加载完成
  waitForDOMContentLoaded: function() {
    return new Promise((resolve) => {
      if (document.readyState === 'interactive' || document.readyState === 'complete') {
        resolve();
      } else {
        document.addEventListener('DOMContentLoaded', resolve, { once: true });
      }
    });
  },

  // 获取页面性能信息
  getPerformanceInfo: function() {
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      return {
        loadTime: timing.loadEventEnd - timing.navigationStart,
        domReadyTime: timing.domContentLoadedEventEnd - timing.navigationStart,
        firstPaint: timing.responseStart - timing.navigationStart
      };
    }
    return null;
  },

  // 检测网络状态
  getNetworkInfo: function() {
    if (navigator.connection) {
      return {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt,
        saveData: navigator.connection.saveData
      };
    }
    return null;
  },

  // 检测设备信息
  getDeviceInfo: function() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      languages: navigator.languages,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency
    };
  }
}; 