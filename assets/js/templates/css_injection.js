(function() {
  try {
    if (!document.getElementById('__STYLE_ID_PLACEHOLDER__')) {
      document.head.insertAdjacentHTML('beforeend', `__CSS_CONTENT_PLACEHOLDER__`);
      return { success: true, message: 'Styles injected successfully' };
    } else {
      return { success: true, message: 'Styles already exist' };
    }
  } catch (error) {
    return { success: false, error: error.toString() };
  }
})(); 