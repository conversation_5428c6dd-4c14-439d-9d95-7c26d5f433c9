// 内容提取功能
window.contentExtractor = {
  // 提取图片URL
  extractImageUrls: function() {
    const images = document.querySelectorAll('img');
    const urls = [];
    
    images.forEach(img => {
      if (img.src && img.src.trim() !== '') {
        urls.push(img.src);
      }
    });
    
    return urls;
  },
  
  // 提取文本内容
  extractTextContent: function() {
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, div');
    const texts = [];
    
    textElements.forEach(element => {
      const text = element.textContent?.trim();
      if (text && text.length > 10) {
        texts.push({
          text: text,
          tag: element.tagName.toLowerCase(),
          className: element.className
        });
      }
    });
    
    return texts;
  },
  
  // 提取链接
  extractLinks: function() {
    const links = document.querySelectorAll('a[href]');
    const urls = [];
    
    links.forEach(link => {
      if (link.href && link.href.trim() !== '') {
        urls.push({
          url: link.href,
          text: link.textContent?.trim() || '',
          title: link.title || ''
        });
      }
    });
    
    return urls;
  },

  // 提取页面标题
  extractPageTitle: function() {
    return {
      title: document.title,
      h1: document.querySelector('h1')?.textContent?.trim(),
      metaTitle: document.querySelector('meta[property="og:title"]')?.getAttribute('content'),
      metaDescription: document.querySelector('meta[name="description"]')?.getAttribute('content')
    };
  },

  // 提取页面元数据
  extractMetaData: function() {
    const metaElements = document.querySelectorAll('meta');
    const metadata = {};
    
    metaElements.forEach(meta => {
      const name = meta.getAttribute('name') || meta.getAttribute('property');
      const content = meta.getAttribute('content');
      if (name && content) {
        metadata[name] = content;
      }
    });
    
    return metadata;
  },

  // 提取结构化数据
  extractStructuredData: function() {
    const scripts = document.querySelectorAll('script[type="application/ld+json"]');
    const structuredData = [];
    
    scripts.forEach(script => {
      try {
        const data = JSON.parse(script.textContent);
        structuredData.push(data);
      } catch (e) {
        console.warn('Failed to parse structured data:', e);
      }
    });
    
    return structuredData;
  },

  // 提取表格数据
  extractTableData: function() {
    const tables = document.querySelectorAll('table');
    const tableData = [];
    
    tables.forEach((table, index) => {
      const rows = table.querySelectorAll('tr');
      const data = [];
      
      rows.forEach(row => {
        const cells = row.querySelectorAll('td, th');
        const rowData = [];
        cells.forEach(cell => {
          rowData.push(cell.textContent?.trim() || '');
        });
        if (rowData.length > 0) {
          data.push(rowData);
        }
      });
      
      if (data.length > 0) {
        tableData.push({
          index: index,
          data: data
        });
      }
    });
    
    return tableData;
  },

  // 提取表单数据
  extractFormData: function() {
    const forms = document.querySelectorAll('form');
    const formData = [];
    
    forms.forEach((form, index) => {
      const inputs = form.querySelectorAll('input, textarea, select');
      const fields = [];
      
      inputs.forEach(input => {
        fields.push({
          name: input.name || input.id,
          type: input.type,
          value: input.value,
          placeholder: input.placeholder
        });
      });
      
      if (fields.length > 0) {
        formData.push({
          index: index,
          action: form.action,
          method: form.method,
          fields: fields
        });
      }
    });
    
    return formData;
  },

  // 提取页面结构
  extractPageStructure: function() {
    const structure = {
      headings: [],
      sections: [],
      navigation: [],
      footer: []
    };
    
    // 提取标题
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      structure.headings.push({
        level: parseInt(heading.tagName.charAt(1)),
        text: heading.textContent?.trim(),
        id: heading.id
      });
    });
    
    // 提取导航
    const navs = document.querySelectorAll('nav, [role="navigation"]');
    navs.forEach(nav => {
      const links = nav.querySelectorAll('a');
      const navLinks = [];
      links.forEach(link => {
        navLinks.push({
          text: link.textContent?.trim(),
          href: link.href
        });
      });
      structure.navigation.push(navLinks);
    });
    
    // 提取页脚
    const footers = document.querySelectorAll('footer, [role="contentinfo"]');
    footers.forEach(footer => {
      structure.footer.push({
        text: footer.textContent?.trim(),
        links: Array.from(footer.querySelectorAll('a')).map(a => ({
          text: a.textContent?.trim(),
          href: a.href
        }))
      });
    });
    
    return structure;
  },

  // 提取所有内容
  extractAllContent: function() {
    return {
      images: this.extractImageUrls(),
      text: this.extractTextContent(),
      links: this.extractLinks(),
      title: this.extractPageTitle(),
      metadata: this.extractMetaData(),
      structuredData: this.extractStructuredData(),
      tables: this.extractTableData(),
      forms: this.extractFormData(),
      structure: this.extractPageStructure()
    };
  },

  // 根据选择器提取内容
  extractBySelector: function(selector) {
    const elements = document.querySelectorAll(selector);
    const results = [];
    
    elements.forEach(element => {
      results.push({
        tagName: element.tagName.toLowerCase(),
        text: element.textContent?.trim(),
        html: element.innerHTML,
        attributes: this.getElementAttributes(element)
      });
    });
    
    return results;
  },

  // 获取元素属性
  getElementAttributes: function(element) {
    const attributes = {};
    for (let attr of element.attributes) {
      attributes[attr.name] = attr.value;
    }
    return attributes;
  },

  // 提取特定类型的内容
  extractByType: function(type) {
    switch (type.toLowerCase()) {
      case 'images':
        return this.extractImageUrls();
      case 'text':
        return this.extractTextContent();
      case 'links':
        return this.extractLinks();
      case 'tables':
        return this.extractTableData();
      case 'forms':
        return this.extractFormData();
      case 'metadata':
        return this.extractMetaData();
      case 'structure':
        return this.extractPageStructure();
      default:
        return this.extractAllContent();
    }
  }
}; 