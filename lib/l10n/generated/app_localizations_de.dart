// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => 'Bildübersetzer';

  @override
  String get targetLanguage => 'Zielsprache';

  @override
  String get language => 'Sprache';

  @override
  String get displayLanguage => 'Anzeigesprache';

  @override
  String get darkMode => 'Dunkelmodus';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get confirm => 'Bestätigen';

  @override
  String get ok => 'OK';

  @override
  String get delete => 'Löschen';

  @override
  String get rename => 'Umbenennen';

  @override
  String get loading => 'Wird geladen..';

  @override
  String get error => 'Fehler';

  @override
  String get success => 'Erfolg';

  @override
  String get retry => 'Wiederholen';

  @override
  String get account => 'Konto';

  @override
  String get feedback => 'Feedback';

  @override
  String get about => 'Über';

  @override
  String get signOut => 'Abmelden';

  @override
  String get translate => 'Übersetzen';

  @override
  String get translating => 'Übersetzung läuft';

  @override
  String get translated => 'Übersetzt';

  @override
  String get failed => 'Vorgang fehlgeschlagen';

  @override
  String get selectTargetLanguage => 'Standardmäßig übersetzen nach';

  @override
  String get targetLanguageDescription =>
      'Die unten ausgewählte Sprache wird als Standardzielsprache für die Übersetzung verwendet.';

  @override
  String get darkModeDescription =>
      'Wählen Sie Ihren bevorzugten Themenmodus für die App.';

  @override
  String get selectTheme => 'Thema auswählen';

  @override
  String get appLanguageSetting => 'Sprache';

  @override
  String get contactUs => 'Kontaktieren Sie uns';

  @override
  String get appDescription =>
      'Hochwertige Manga- und Bildübersetzung \nmit nahtloser Textüberlagerung';

  @override
  String get feedbackHint => 'Ihr Feedback hilft uns, besser zu werden';

  @override
  String get emailHint => 'E-Mail (optional)';

  @override
  String get send => 'Senden';

  @override
  String get deleteData => 'Daten löschen';

  @override
  String get deleteDataWarningTitle => 'Daten löschen?';

  @override
  String get deleteDataWarningText =>
      'Ihr Konto und alle Daten werden gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.';

  @override
  String get done => 'Fertig';

  @override
  String get deleteDataSuccess => 'Ihre Daten wurden vollständig gelöscht.';

  @override
  String get signIn => 'Anmelden';

  @override
  String get browserSettings => 'Browser-Einstellungen';

  @override
  String get adBlocking => 'Werbeblocker';

  @override
  String get adBlockingDescription =>
      'Blockieren Sie Werbung und Tracker beim Surfen';

  @override
  String get adBlockingEnabled => 'Werbeblocker aktiviert';

  @override
  String get adBlockingDisabled => 'Werbeblocker deaktiviert';

  @override
  String get enterUrl => 'URL oder Suchbegriffe eingeben';

  @override
  String get errorLoadingImage => 'Fehler beim Laden des Bildes';

  @override
  String get selectImages => 'Bitte wählen Sie mindestens ein Bild aus';

  @override
  String get noValidImages => 'Keine gültigen Bilder zum Herunterladen';

  @override
  String processingError(String error) {
    return 'Fehler bei der Verarbeitung von Bildern: $error';
  }

  @override
  String get autoRenew => 'Automatische Verlängerung, jederzeit kündbar';

  @override
  String get privacyPolicy => 'Datenschutzrichtlinie';

  @override
  String get userAgreement => 'Benutzervereinbarung';

  @override
  String get operationTakingLong => 'Der Vorgang dauert länger als erwartet';

  @override
  String get system => 'System';

  @override
  String get light => 'Hell';

  @override
  String get dark => 'Dunkel';

  @override
  String get chinese => 'Chinesisch';

  @override
  String get english => 'Englisch';

  @override
  String get japanese => 'Japanisch';

  @override
  String get korean => 'Koreanisch';

  @override
  String get french => 'Französisch';

  @override
  String get german => 'Deutsch';

  @override
  String get spanish => 'Spanisch';

  @override
  String get italian => 'Italienisch';

  @override
  String get thai => 'Thailändisch';

  @override
  String get vietnamese => 'Vietnamesisch';

  @override
  String get indonesian => 'Indonesisch';

  @override
  String get malay => 'Malaiisch';

  @override
  String get feedbackSuccess => 'Vielen Dank für Ihr Feedback!';

  @override
  String get feedbackError =>
      'Feedback konnte nicht gesendet werden. Bitte versuchen Sie es erneut.';

  @override
  String get feedbackEmpty => 'Entschuldigung, aber der Inhalt ist leer';

  @override
  String get feedbackSendError => 'Feedback konnte nicht gesendet werden';

  @override
  String get generalError =>
      'Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';

  @override
  String get deleteAccount => 'Konto löschen';

  @override
  String get deleteAccountWarning =>
      'Diese Aktion kann nicht rückgängig gemacht werden. Alle Ihre Daten werden dauerhaft gelöscht.';

  @override
  String get confirmDelete => 'Löschen bestätigen';

  @override
  String get deleteSuccess => 'Konto erfolgreich gelöscht';

  @override
  String get deleteError =>
      'Konto konnte nicht gelöscht werden. Bitte versuchen Sie es erneut.';

  @override
  String get subscriptionDescription =>
      'Automatische Verlängerung, jederzeit kündbar';

  @override
  String get subscribe => 'Abonnieren';

  @override
  String get restore => 'Wiederherstellen';

  @override
  String get termsOfService => 'Nutzungsbedingungen';

  @override
  String get monthly => 'Monatlich';

  @override
  String get mostPopular => 'Am beliebtesten';

  @override
  String get bestValue => 'Bestes Angebot';

  @override
  String get unlimitedManga => 'Unbegrenzte Manga-Generierung';

  @override
  String get highQuality => 'Hochwertige Bilder';

  @override
  String get prioritySupport => 'Vorrangiger Support';

  @override
  String get noAds => 'Keine Werbung';

  @override
  String get popular => 'Beliebt';

  @override
  String get freeTrial => 'Kostenlose Testversion';

  @override
  String freeTrialDescription(String price) {
    return 'Genießen Sie eine 3-tägige kostenlose Testversion, dann $price wöchentlich';
  }

  @override
  String get fileUploadFeature =>
      'Unterstützung für Datei-Upload zur Übersetzung';

  @override
  String get higherResolutionFeature => 'Höhere Auflösung';

  @override
  String get accurateTranslationFeature => 'Präzisere Übersetzung';

  @override
  String get unlimitedTranslationsFeature =>
      'Unbegrenzte tägliche Bildübersetzungen';

  @override
  String get adFreeFeature => 'Werbefreie Nutzung';

  @override
  String get accountError => 'Kontofehler';

  @override
  String get noProductsError => 'Keine Produkte verfügbar';

  @override
  String get processing => 'Verarbeitung...';

  @override
  String get purchaseFailed => 'Kauf fehlgeschlagen';

  @override
  String get restoring => 'Käufe werden wiederhergestellt...';

  @override
  String get tryAgainLater => 'Bitte versuchen Sie es später erneut';

  @override
  String get networkError => 'Netzwerkfehler';

  @override
  String get importFile => 'Datei importieren';

  @override
  String get album => 'Album';

  @override
  String get camera => 'Kamera';

  @override
  String get translateWebImages => 'Web-Bilder übersetzen';

  @override
  String get recentTranslations => 'Letzte Übersetzungen';

  @override
  String get seeAll => 'Alle anzeigen';

  @override
  String get noTranslationHistory => 'Noch keine Übersetzungshistorie';

  @override
  String get allFiles => 'Alle Dateien';

  @override
  String get viewMode => 'Ansichtsmodus';

  @override
  String get listMode => 'Listenmodus';

  @override
  String get smallGridMode => 'Kleiner Raster-Modus';

  @override
  String get largeGridMode => 'Großer Raster-Modus';

  @override
  String get listModeDescription => 'Kompakte Listendarstellung';

  @override
  String get smallGridModeDescription => 'Kleine Rastergröße';

  @override
  String get largeGridModeDescription => 'Große Wasserfall-Darstellung';

  @override
  String get singleImage => 'Einzelbild';

  @override
  String multipleImages(int count) {
    return '$count Bilder';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count';
  }

  @override
  String selectedCount(int count) {
    return '$count ausgewählt';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '$count/$total ausgewählt';
  }

  @override
  String get noFilesFound => 'Keine Dateien gefunden';

  @override
  String get download => 'Herunterladen';

  @override
  String get downloading => 'Wird heruntergeladen...';

  @override
  String get imageSavedToGallery => 'Bild in Galerie gespeichert';

  @override
  String get failedToSaveImage => 'Bild konnte nicht gespeichert werden';

  @override
  String get noImagesToSave => 'Keine Bilder zum Speichern';

  @override
  String get imageIndexOutOfRange => 'Bildindex außerhalb des Bereichs';

  @override
  String get noTranslationResultSavingOriginal =>
      'Kein Übersetzungsergebnis, speichere Originalbild';

  @override
  String get imageContentNotFound => 'Bildinhalt konnte nicht abgerufen werden';

  @override
  String get imageDataGenerationFailed => 'Bilddatenerstellung fehlgeschlagen';

  @override
  String get imageSavedSuccessfully => 'Bild erfolgreich gespeichert';

  @override
  String get savingFailed => 'Speichern fehlgeschlagen';

  @override
  String get originalImageSavedSuccessfully =>
      'Originalbild erfolgreich gespeichert';

  @override
  String networkRequestFailed(String statusCode) {
    return 'Netzwerkanfrage fehlgeschlagen: $statusCode';
  }

  @override
  String get cannotGetImageData => 'Bilddaten konnten nicht abgerufen werden';

  @override
  String saveOriginalImageFailed(String error) {
    return 'Originalbild konnte nicht gespeichert werden: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext =>
      'Speichern des übersetzten Bildes erfordert Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      'Speichern übersetzter Bilder erfordert RepaintBoundary-Implementierung, verwenden Sie die saveCurrentDisplayImage-Methode';

  @override
  String saveTranslatedImageFailed(String error) {
    return 'Übersetztes Bild konnte nicht gespeichert werden: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count Bilder erfolgreich gespeichert';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount erfolgreich gespeichert, $failureCount fehlgeschlagen';
  }

  @override
  String errorDownloading(String error) {
    return 'Fehler beim Herunterladen: $error';
  }

  @override
  String loadingFailed(String error) {
    return 'Laden fehlgeschlagen $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return '$count Element(e) löschen?';
  }

  @override
  String get drafts => 'Ausstehende Aufgaben';

  @override
  String toLanguage(String lang) {
    return 'Nach: $lang';
  }

  @override
  String get translateAll => 'Alle übersetzen';

  @override
  String get nothingSelected => 'Nichts ausgewählt';

  @override
  String operationFailed(String error) {
    return 'Fehlgeschlagen: $error';
  }

  @override
  String get allJobsDone => 'Alle Aufgaben erledigt';

  @override
  String get imageNotFound => 'Bild nicht gefunden';

  @override
  String usesLeftToday(int count) {
    return '$count Nutzungen heute übrig';
  }

  @override
  String get upgradeToPro => 'Upgrade auf PRO für unbegrenzten Zugriff';

  @override
  String get upgradeNow => 'Jetzt upgraden';

  @override
  String get updatingTranslationPosition =>
      'Übersetzungsposition wird aktualisiert...';

  @override
  String get noImagesFound =>
      'Keine Bilder gefunden oder Lazy Loading in Bearbeitung';

  @override
  String get localTranslationInitializing =>
      'Lokale Übersetzungsdienste werden initialisiert...';

  @override
  String get localTranslationFailed => 'Lokale Übersetzung fehlgeschlagen';

  @override
  String get ocrServiceNotInitialized => 'OCR-Dienst nicht initialisiert';

  @override
  String get translationServiceNotInitialized =>
      'Übersetzungsdienst nicht initialisiert';

  @override
  String get processingImagesForTranslation =>
      'Bilder werden für Übersetzung verarbeitet...';

  @override
  String localTranslationCompleted(int count) {
    return 'Lokale Übersetzung für $count Bilder abgeschlossen';
  }

  @override
  String get newTab => 'Neuer Tab';

  @override
  String get addBookmark => 'Lesezeichen hinzufügen';

  @override
  String get removeBookmark => 'Lesezeichen entfernen';

  @override
  String get unableToProcessImages =>
      'Es konnten keine Bilder auf der Seite zur Verarbeitung gefunden werden.';

  @override
  String downloadFailed(String error) {
    return 'Download fehlgeschlagen: $error';
  }

  @override
  String get selectAtLeastOneImage =>
      'Bitte wählen Sie mindestens ein Bild aus';

  @override
  String get noValidImagesToDownload =>
      'Keine gültigen Bilder zum Herunterladen';

  @override
  String failedToProcessImages(String error) {
    return 'Fehler bei der Verarbeitung von Bildern: $error';
  }

  @override
  String get loadingImages => 'Bilder werden geladen...';

  @override
  String get deleteThisItem => 'Dieses Element löschen?';

  @override
  String get errorLoadingImageViewer => 'Fehler beim Laden des Bildanzeigers';

  @override
  String get failedToDeleteImage => 'Bild konnte nicht gelöscht werden';

  @override
  String completedTranslationAt(String time) {
    return 'Übersetzung abgeschlossen am \n$time';
  }

  @override
  String get dailyLimitReached => 'Tageslimit erreicht';

  @override
  String get quotaResetMessage =>
      'Kostenlos-Nutzung resetet morgen. Werbung gibt Extras';

  @override
  String get upgradeButton => 'Upgraden';

  @override
  String get tryTomorrowButton => 'Morgen versuchen';

  @override
  String get followSystem => 'System folgen';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => 'Dateiübersetzung freischalten';

  @override
  String get highQualityTranslation => 'Hochwertige Übersetzung';

  @override
  String get adFreeExperience => 'Werbefreie Nutzung';

  @override
  String get getPro => 'PRO ERHALTEN';

  @override
  String get loadFailed =>
      'Laden fehlgeschlagen, bitte überprüfen Sie Ihre Netzwerkverbindung';

  @override
  String get back => 'Zurück';

  @override
  String get purchaseSuccessful => 'Kauf erfolgreich';

  @override
  String get purchaseRestored => 'Kauf wiederhergestellt';

  @override
  String restoreFailed(String error) {
    return 'Wiederherstellung fehlgeschlagen: $error';
  }

  @override
  String get weekly => 'Wöchentlich';

  @override
  String get annual => 'Jährlich';

  @override
  String get free => 'KOSTENLOS';

  @override
  String get freeText => '3 Tage kostenlose Testversion';

  @override
  String get billedMonthly => 'Monatliche Abrechnung';

  @override
  String get billedAnnual => 'Jährliche Abrechnung';

  @override
  String get billedWeekly => 'Wöchentliche Abrechnung';

  @override
  String get freeTrialText => '3 Tage kostenlose Testversion';

  @override
  String get save30Percent => '30% sparen';

  @override
  String get loginTitle => 'Anmelden bei Ihrem\n Konto';

  @override
  String get watchAd => 'Mehr Holen';

  @override
  String get watchAdDescription =>
      'Sehen Sie sich eine Werbung an, um mehr Nutzungskontingent zu erhalten';

  @override
  String get fontSettings => 'Schrifteinstellungen';

  @override
  String get fontFamily => 'Schriftart';

  @override
  String get strokeSettings => 'Umrisseinstellungen';

  @override
  String get textStyleSettings => 'Textstil-Einstellungen';

  @override
  String get resetToDefaults => 'Auf Standard zurücksetzen';

  @override
  String get fontSize => 'Schriftgröße';

  @override
  String get fontWeight => 'Schriftstärke';

  @override
  String get textColor => 'Textfarbe';

  @override
  String get textLabel => 'Text';

  @override
  String get strokeLabel => 'Umriss';

  @override
  String get strokeWidth => 'Umrissbreite';

  @override
  String get shadowEffect => 'Schatteneffekt';

  @override
  String get opacity => 'Transparenz';

  @override
  String get horizontalOffset => 'Horizontaler Versatz';

  @override
  String get verticalOffset => 'Vertikaler Versatz';

  @override
  String get blurRadius => 'Unschärferadius';

  @override
  String get fontWeightThin => 'Dünn';

  @override
  String get fontWeightNormal => 'Normal';

  @override
  String get fontWeightMedium => 'Mittel';

  @override
  String get fontWeightSemiBold => 'Halbfett';

  @override
  String get fontWeightBold => 'Fett';

  @override
  String get fontWeightExtraBold => 'Extrafett';

  @override
  String get fontWeightBlack => 'Schwarz';

  @override
  String get recommendedWebsites => 'Empfohlene Websites';

  @override
  String get myBookmarks => 'Meine Lesezeichen';

  @override
  String get noBookmarks => 'Keine Lesezeichen';

  @override
  String get bookmarkAdded => 'Lesezeichen hinzugefügt';

  @override
  String get alreadyBookmarked => 'Bereits als Lesezeichen gespeichert';

  @override
  String get cannotBookmarkEmptyPage =>
      'Leere Seite kann nicht als Lesezeichen gespeichert werden';

  @override
  String get bookmarkFailed => 'Lesezeichen hinzufügen fehlgeschlagen';

  @override
  String get deleteBookmark => 'Lesezeichen löschen';

  @override
  String deleteBookmarkConfirm(String title) {
    return 'Sind Sie sicher, dass Sie das Lesezeichen \"$title\" löschen möchten?';
  }

  @override
  String get bookmarkDeleted => 'Lesezeichen gelöscht';

  @override
  String get manageBookmarks => 'Verwalten';

  @override
  String get allBookmarks => 'Alle Lesezeichen';

  @override
  String get browsingHistory => 'Verlauf';

  @override
  String get noBrowsingHistory => 'Noch kein Browserverlauf';

  @override
  String get deleteHistory => 'Verlauf löschen';

  @override
  String get deleteHistoryConfirm =>
      'Sind Sie sicher, dass Sie diesen Verlaufseintrag löschen möchten?';

  @override
  String get historyDeleted => 'Verlaufseintrag gelöscht';

  @override
  String get justNow => 'Gerade eben';

  @override
  String minutesAgo(int count) {
    return 'vor $count Minuten';
  }

  @override
  String hoursAgo(int count) {
    return 'vor $count Stunden';
  }

  @override
  String daysAgo(int count) {
    return 'vor $count Tagen';
  }

  @override
  String downloadingLanguageFiles(String languageName) {
    return 'Sprachdateien werden heruntergeladen ($languageName)';
  }

  @override
  String get translationInProgress => 'Übersetzung läuft...';

  @override
  String get translateThisImage => 'Dieses Bild übersetzen';

  @override
  String get imageTranslationLoading => 'Bild wird übersetzt...';

  @override
  String get downloadTimeout =>
      'Download-Zeitüberschreitung, bitte überprüfen Sie Ihre Netzwerkverbindung';
}
