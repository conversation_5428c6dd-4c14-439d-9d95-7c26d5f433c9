// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => '图片翻译器';

  @override
  String get targetLanguage => '翻译到';

  @override
  String get language => '语言设置';

  @override
  String get displayLanguage => '显示语言';

  @override
  String get darkMode => '深色模式';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '确认';

  @override
  String get ok => '确定';

  @override
  String get delete => '删除';

  @override
  String get rename => '重命名';

  @override
  String get loading => '加载中..';

  @override
  String get error => '错误';

  @override
  String get success => '成功';

  @override
  String get retry => '重试';

  @override
  String get account => '账户';

  @override
  String get feedback => '反馈';

  @override
  String get about => '关于';

  @override
  String get signOut => '退出登录';

  @override
  String get translate => '翻译';

  @override
  String get translating => '翻译中';

  @override
  String get translated => '已翻译';

  @override
  String get failed => '操作失败';

  @override
  String get selectTargetLanguage => '默认翻译到';

  @override
  String get targetLanguageDescription => '默认的翻译目标语言';

  @override
  String get darkModeDescription => '选择您喜欢的应用主题模式';

  @override
  String get selectTheme => '选择主题';

  @override
  String get appLanguageSetting => '语言';

  @override
  String get contactUs => '联系我们';

  @override
  String get appDescription => '高质量漫画和图片翻译';

  @override
  String get feedbackHint => '您的反馈帮助我们改进';

  @override
  String get emailHint => '邮箱（选填）';

  @override
  String get send => '发送';

  @override
  String get deleteData => '删除数据';

  @override
  String get deleteDataWarningTitle => '删除全部数据';

  @override
  String get deleteDataWarningText => '您的账号和所有数据将被删除，此操作无法撤销';

  @override
  String get done => '完成';

  @override
  String get deleteDataSuccess => '您的数据已被完全删除';

  @override
  String get signIn => '登录';

  @override
  String get browserSettings => '浏览器设置';

  @override
  String get adBlocking => '广告拦截';

  @override
  String get adBlockingDescription => '浏览网页时拦截广告和跟踪器';

  @override
  String get adBlockingEnabled => '已启用广告拦截';

  @override
  String get adBlockingDisabled => '已禁用广告拦截';

  @override
  String get enterUrl => '输入网址或搜索关键词';

  @override
  String get errorLoadingImage => '加载图片失败';

  @override
  String get selectImages => '请至少选择一张图片';

  @override
  String get noValidImages => '没有可下载的有效图片';

  @override
  String processingError(String error) {
    return '处理图片失败：$error';
  }

  @override
  String get autoRenew => '自动续订，随时可取消';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get userAgreement => '用户协议';

  @override
  String get operationTakingLong => '操作时间超出预期';

  @override
  String get system => '系统';

  @override
  String get light => '浅色';

  @override
  String get dark => '深色';

  @override
  String get chinese => '中文';

  @override
  String get english => '英语';

  @override
  String get japanese => '日语';

  @override
  String get korean => '韩语';

  @override
  String get french => '法语';

  @override
  String get german => '德语';

  @override
  String get spanish => '西班牙语';

  @override
  String get italian => '意大利语';

  @override
  String get thai => '泰语';

  @override
  String get vietnamese => '越南语';

  @override
  String get indonesian => '印尼语';

  @override
  String get malay => '马来语';

  @override
  String get feedbackSuccess => '感谢您的反馈！';

  @override
  String get feedbackError => '提交反馈失败，请重试。';

  @override
  String get feedbackEmpty => '抱歉，内容不能为空';

  @override
  String get feedbackSendError => '发送反馈失败';

  @override
  String get generalError => '发生错误。请重试。';

  @override
  String get deleteAccount => '删除账号';

  @override
  String get deleteAccountWarning => '此操作无法撤销，您的所有数据将被永久删除';

  @override
  String get confirmDelete => '确认删除';

  @override
  String get deleteSuccess => '账户删除成功';

  @override
  String get deleteError => '删除账户失败，请重试。';

  @override
  String get subscriptionDescription => '自动续订，随时可取消';

  @override
  String get subscribe => '订阅';

  @override
  String get restore => '恢复购买';

  @override
  String get termsOfService => '服务条款';

  @override
  String get monthly => '月度';

  @override
  String get mostPopular => '最受欢迎';

  @override
  String get bestValue => '特惠';

  @override
  String get unlimitedManga => '无限漫画生成';

  @override
  String get highQuality => '高质量图片';

  @override
  String get prioritySupport => '优先支持';

  @override
  String get noAds => '无广告';

  @override
  String get popular => '热门';

  @override
  String get freeTrial => '免费试用';

  @override
  String freeTrialDescription(String price) {
    return '享受3天免费试用，之后每周$price';
  }

  @override
  String get fileUploadFeature => '支持文件上传翻译';

  @override
  String get higherResolutionFeature => '更高分辨率';

  @override
  String get accurateTranslationFeature => '更准确的翻译';

  @override
  String get unlimitedTranslationsFeature => '每日无限图片翻译';

  @override
  String get adFreeFeature => '无广告体验';

  @override
  String get accountError => '账户错误';

  @override
  String get noProductsError => '暂无可用产品';

  @override
  String get processing => '处理中..';

  @override
  String get purchaseFailed => '购买失败';

  @override
  String get restoring => '恢复购买中...';

  @override
  String get tryAgainLater => '请稍后重试';

  @override
  String get networkError => '网络错误';

  @override
  String get importFile => '导入文件';

  @override
  String get album => '相册';

  @override
  String get camera => '相机';

  @override
  String get translateWebImages => '翻译网页图片';

  @override
  String get recentTranslations => '最近翻译';

  @override
  String get seeAll => '查看全部';

  @override
  String get noTranslationHistory => '暂无翻译历史';

  @override
  String get allFiles => '所有文件';

  @override
  String get viewMode => '查看方式';

  @override
  String get listMode => '列表模式';

  @override
  String get smallGridMode => '小图模式';

  @override
  String get largeGridMode => '大图模式';

  @override
  String get listModeDescription => '紧凑的列表显示';

  @override
  String get smallGridModeDescription => '小尺寸网格显示';

  @override
  String get largeGridModeDescription => '大尺寸瀑布流显示';

  @override
  String get singleImage => '单张图片';

  @override
  String multipleImages(int count) {
    return '$count 张图片';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count张';
  }

  @override
  String selectedCount(int count) {
    return '已选择 $count 项';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '已选择 $count/$total';
  }

  @override
  String get noFilesFound => '未找到文件';

  @override
  String get download => '下载';

  @override
  String get downloading => '下载中..';

  @override
  String get imageSavedToGallery => '图片已保存到相册';

  @override
  String get failedToSaveImage => '保存图片失败';

  @override
  String get noImagesToSave => '没有可保存的图片';

  @override
  String get imageIndexOutOfRange => '图片索引超出范围';

  @override
  String get noTranslationResultSavingOriginal => '该图片没有翻译结果，将保存原图';

  @override
  String get imageContentNotFound => '无法获取图片内容';

  @override
  String get imageDataGenerationFailed => '图片数据生成失败';

  @override
  String get imageSavedSuccessfully => '图片保存成功';

  @override
  String get savingFailed => '保存失败';

  @override
  String get originalImageSavedSuccessfully => '原图保存成功';

  @override
  String networkRequestFailed(String statusCode) {
    return '网络请求失败: $statusCode';
  }

  @override
  String get cannotGetImageData => '无法获取图片数据';

  @override
  String saveOriginalImageFailed(String error) {
    return '保存原图失败: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext => '保存翻译图片需要Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      '翻译图片保存需要通过RepaintBoundary实现，请使用saveCurrentDisplayImage方法';

  @override
  String saveTranslatedImageFailed(String error) {
    return '保存翻译图片失败: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '成功保存 $count 张图片';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '成功保存 $successCount 张，失败 $failureCount 张';
  }

  @override
  String errorDownloading(String error) {
    return '下载失败：$error';
  }

  @override
  String loadingFailed(String error) {
    return '加载失败：$error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return '删除 $count 项？';
  }

  @override
  String get drafts => '待处理任务';

  @override
  String toLanguage(String lang) {
    return '翻译到：$lang';
  }

  @override
  String get translateAll => '全部翻译';

  @override
  String get nothingSelected => '未选择任何项目';

  @override
  String operationFailed(String error) {
    return '操作失败：$error';
  }

  @override
  String get allJobsDone => '所有任务已完成';

  @override
  String get imageNotFound => '图片未找到';

  @override
  String usesLeftToday(int count) {
    return '今日剩余 $count 次';
  }

  @override
  String get upgradeToPro => '升级到Pro获得无限使用';

  @override
  String get upgradeNow => '升级PRO';

  @override
  String get updatingTranslationPosition => '正在更新翻译位置...';

  @override
  String get noImagesFound => '未找到图片或正在加载中';

  @override
  String get localTranslationInitializing => '正在初始化本地翻译服务...';

  @override
  String get localTranslationFailed => '本地翻译失败';

  @override
  String get ocrServiceNotInitialized => 'OCR服务未初始化';

  @override
  String get translationServiceNotInitialized => '翻译服务未初始化';

  @override
  String get processingImagesForTranslation => '正在处理图片进行翻译...';

  @override
  String localTranslationCompleted(int count) {
    return '已完成 $count 张图片的本地翻译';
  }

  @override
  String get newTab => '新建标签页';

  @override
  String get addBookmark => '添加书签';

  @override
  String get removeBookmark => '移除书签';

  @override
  String get unableToProcessImages => '无法在页面上找到可处理的图片';

  @override
  String downloadFailed(String error) {
    return '下载失败：$error';
  }

  @override
  String get selectAtLeastOneImage => '请至少选择一张图片';

  @override
  String get noValidImagesToDownload => '没有可下载的有效图片';

  @override
  String failedToProcessImages(String error) {
    return '处理图片失败：$error';
  }

  @override
  String get loadingImages => '正在加载图片...';

  @override
  String get deleteThisItem => '删除此项目？';

  @override
  String get errorLoadingImageViewer => '加载失败';

  @override
  String get failedToDeleteImage => '删除图片失败';

  @override
  String completedTranslationAt(String time) {
    return '翻译完成于 \n$time';
  }

  @override
  String get dailyLimitReached => '今日免费使用次数已达上限';

  @override
  String get quotaResetMessage => '免费额度将于明天重置，观看广告可以获取更多免费次数';

  @override
  String get upgradeButton => '立即升级';

  @override
  String get tryTomorrowButton => '明天再试';

  @override
  String get followSystem => '跟随系统';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => '解锁文件翻译';

  @override
  String get highQualityTranslation => '高质量翻译';

  @override
  String get adFreeExperience => '无广告体验';

  @override
  String get getPro => '升级PRO';

  @override
  String get loadFailed => '加载失败，请检查网络连接';

  @override
  String get back => '返回';

  @override
  String get purchaseSuccessful => '购买成功';

  @override
  String get purchaseRestored => '购买已恢复';

  @override
  String restoreFailed(String error) {
    return '恢复失败：$error';
  }

  @override
  String get weekly => '每周';

  @override
  String get annual => '每年';

  @override
  String get free => '免费';

  @override
  String get freeText => '3天免费试用';

  @override
  String get billedMonthly => '按月计费';

  @override
  String get billedAnnual => '按年计费';

  @override
  String get billedWeekly => '按周计费';

  @override
  String get freeTrialText => '3天免费试用';

  @override
  String get save30Percent => '节省30%';

  @override
  String get loginTitle => '登录到您的\n账户';

  @override
  String get watchAd => '领取更多';

  @override
  String get watchAdDescription => '观看广告获得更多使用配额';

  @override
  String get fontSettings => '字体设置';

  @override
  String get fontFamily => '字体系列';

  @override
  String get strokeSettings => '描边设置';

  @override
  String get textStyleSettings => '文本样式设置';

  @override
  String get resetToDefaults => '重置为默认设置';

  @override
  String get fontSize => '字体大小';

  @override
  String get fontWeight => '字体粗细';

  @override
  String get textColor => '文本颜色';

  @override
  String get textLabel => '文字';

  @override
  String get strokeLabel => '描边';

  @override
  String get strokeWidth => '描边宽度';

  @override
  String get shadowEffect => '阴影效果';

  @override
  String get opacity => '透明度';

  @override
  String get horizontalOffset => '水平偏移';

  @override
  String get verticalOffset => '垂直偏移';

  @override
  String get blurRadius => '模糊半径';

  @override
  String get fontWeightThin => '细体';

  @override
  String get fontWeightNormal => '正常';

  @override
  String get fontWeightMedium => '中等';

  @override
  String get fontWeightSemiBold => '半粗';

  @override
  String get fontWeightBold => '粗体';

  @override
  String get fontWeightExtraBold => '特粗';

  @override
  String get fontWeightBlack => '超粗';

  @override
  String get recommendedWebsites => '推荐网站';

  @override
  String get myBookmarks => '我的收藏';

  @override
  String get noBookmarks => '暂无收藏';

  @override
  String get bookmarkAdded => '已添加到收藏';

  @override
  String get alreadyBookmarked => '已收藏过此页面';

  @override
  String get cannotBookmarkEmptyPage => '无法收藏空白页面';

  @override
  String get bookmarkFailed => '收藏失败';

  @override
  String get deleteBookmark => '删除书签';

  @override
  String deleteBookmarkConfirm(String title) {
    return '确定要删除书签 \"$title\" 吗？';
  }

  @override
  String get bookmarkDeleted => '书签已删除';

  @override
  String get manageBookmarks => '管理';

  @override
  String get allBookmarks => '全部收藏';

  @override
  String get browsingHistory => '浏览历史';

  @override
  String get noBrowsingHistory => '暂无浏览历史';

  @override
  String get deleteHistory => '删除历史记录';

  @override
  String get deleteHistoryConfirm => '确定要删除此历史记录吗？';

  @override
  String get historyDeleted => '历史记录已删除';

  @override
  String get justNow => '刚刚';

  @override
  String minutesAgo(int count) {
    return '$count分钟前';
  }

  @override
  String hoursAgo(int count) {
    return '$count小时前';
  }

  @override
  String daysAgo(int count) {
    return '$count天前';
  }

  @override
  String downloadingLanguageFiles(String languageName) {
    return '正在下载语言文件 ($languageName)';
  }

  @override
  String get translationInProgress => '翻译进行中...';

  @override
  String get translateThisImage => '翻译此图片';

  @override
  String get imageTranslationLoading => '正在翻译图片...';
}

/// The translations for Chinese, using the Han script (`zh_Hant`).
class AppLocalizationsZhHant extends AppLocalizationsZh {
  AppLocalizationsZhHant() : super('zh_Hant');

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => '圖片翻譯器';

  @override
  String get targetLanguage => '目標語言';

  @override
  String get language => '語言';

  @override
  String get displayLanguage => '顯示語言';

  @override
  String get darkMode => '深色模式';

  @override
  String get cancel => '取消';

  @override
  String get confirm => '確認';

  @override
  String get ok => '確定';

  @override
  String get delete => '刪除';

  @override
  String get rename => '重新命名';

  @override
  String get loading => '載入中...';

  @override
  String get error => '錯誤';

  @override
  String get success => '成功';

  @override
  String get retry => '重試';

  @override
  String get account => '帳號';

  @override
  String get feedback => '意見回饋';

  @override
  String get about => '關於';

  @override
  String get signOut => '登出';

  @override
  String get translate => '翻譯';

  @override
  String get translating => '翻譯中';

  @override
  String get translated => '已翻譯';

  @override
  String get failed => '操作失敗';

  @override
  String get selectTargetLanguage => '預設翻譯至';

  @override
  String get targetLanguageDescription => '下方選擇的語言將作為預設翻譯目標語言。';

  @override
  String get darkModeDescription => '選擇您偏好的應用程式主題模式。';

  @override
  String get selectTheme => '選擇主題';

  @override
  String get appLanguageSetting => '語言';

  @override
  String get contactUs => '聯絡我們';

  @override
  String get appDescription => '高品質漫畫與圖片翻譯\n無縫文字覆蓋';

  @override
  String get feedbackHint => '您的意見有助於我們改進';

  @override
  String get emailHint => '電子郵件（選填）';

  @override
  String get send => '送出';

  @override
  String get deleteData => '刪除資料';

  @override
  String get deleteDataWarningTitle => '刪除資料？';

  @override
  String get deleteDataWarningText => '您的帳號與所有資料將被刪除，此操作無法復原。';

  @override
  String get done => '完成';

  @override
  String get deleteDataSuccess => '您的資料已完全刪除。';

  @override
  String get signIn => '登入';

  @override
  String get browserSettings => '瀏覽器設定';

  @override
  String get adBlocking => '廣告攔截';

  @override
  String get adBlockingDescription => '瀏覽網頁時攔截廣告和追蹤器';

  @override
  String get adBlockingEnabled => '已啟用廣告攔截';

  @override
  String get adBlockingDisabled => '已停用廣告攔截';

  @override
  String get enterUrl => '輸入網址或搜尋關鍵詞';

  @override
  String get errorLoadingImage => '圖片載入錯誤';

  @override
  String get selectImages => '請至少選擇一張圖片';

  @override
  String get noValidImages => '沒有可下載的有效圖片';

  @override
  String processingError(String error) {
    return '處理圖片失敗：$error';
  }

  @override
  String get autoRenew => '自動續訂，隨時可取消';

  @override
  String get privacyPolicy => '隱私政策';

  @override
  String get userAgreement => '用戶協議';

  @override
  String get operationTakingLong => '操作時間超出預期';

  @override
  String get system => '系統';

  @override
  String get light => '淺色';

  @override
  String get dark => '深色';

  @override
  String get chinese => '中文';

  @override
  String get english => '英文';

  @override
  String get japanese => '日文';

  @override
  String get korean => '韓文';

  @override
  String get french => '法文';

  @override
  String get german => '德文';

  @override
  String get spanish => '西班牙文';

  @override
  String get italian => '義大利文';

  @override
  String get thai => '泰文';

  @override
  String get vietnamese => '越南文';

  @override
  String get indonesian => '印尼文';

  @override
  String get malay => '馬來文';

  @override
  String get feedbackSuccess => '感謝您的回饋！';

  @override
  String get feedbackError => '意見送出失敗，請再試一次。';

  @override
  String get feedbackEmpty => '抱歉，內容不能為空';

  @override
  String get feedbackSendError => '發送反饋失敗';

  @override
  String get generalError => '發生錯誤。請重試。';

  @override
  String get deleteAccount => '刪除帳號';

  @override
  String get deleteAccountWarning => '此操作無法復原，所有資料將被永久刪除。';

  @override
  String get confirmDelete => '確認刪除';

  @override
  String get deleteSuccess => '帳號刪除成功';

  @override
  String get deleteError => '帳號刪除失敗，請再試一次。';

  @override
  String get subscriptionDescription => '自動續訂，隨時可取消';

  @override
  String get subscribe => '訂閱';

  @override
  String get restore => '恢復購買';

  @override
  String get termsOfService => '服務條款';

  @override
  String get monthly => '每月';

  @override
  String get mostPopular => '最受歡迎';

  @override
  String get bestValue => '最佳價值';

  @override
  String get unlimitedManga => '無限漫畫生成';

  @override
  String get highQuality => '高品質圖片';

  @override
  String get prioritySupport => '優先支援';

  @override
  String get noAds => '無廣告';

  @override
  String get popular => '熱門';

  @override
  String get freeTrial => '免費試用';

  @override
  String freeTrialDescription(String price) {
    return '享受 3 天免費試用，之後每週 $price';
  }

  @override
  String get fileUploadFeature => '支援檔案上傳翻譯';

  @override
  String get higherResolutionFeature => '更高解析度';

  @override
  String get accurateTranslationFeature => '更準確的翻譯';

  @override
  String get unlimitedTranslationsFeature => '每日無限圖片翻譯';

  @override
  String get adFreeFeature => '無廣告體驗';

  @override
  String get accountError => '帳號錯誤';

  @override
  String get noProductsError => '暫無可用產品';

  @override
  String get processing => '處理中...';

  @override
  String get purchaseFailed => '購買失敗';

  @override
  String get restoring => '正在恢復購買...';

  @override
  String get tryAgainLater => '請稍後再試';

  @override
  String get networkError => '網路錯誤';

  @override
  String get importFile => '匯入檔案';

  @override
  String get album => '相簿';

  @override
  String get camera => '相機';

  @override
  String get translateWebImages => '翻譯網頁圖片';

  @override
  String get recentTranslations => '最近翻譯';

  @override
  String get seeAll => '查看全部';

  @override
  String get noTranslationHistory => '尚無翻譯紀錄';

  @override
  String get allFiles => '所有檔案';

  @override
  String get viewMode => '檢視方式';

  @override
  String get listMode => '列表模式';

  @override
  String get smallGridMode => '小圖模式';

  @override
  String get largeGridMode => '大圖模式';

  @override
  String get listModeDescription => '緊湊的列表顯示';

  @override
  String get smallGridModeDescription => '小尺寸網格顯示';

  @override
  String get largeGridModeDescription => '大尺寸瀑布流顯示';

  @override
  String get singleImage => '單張圖片';

  @override
  String multipleImages(int count) {
    return '$count 張圖片';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count張';
  }

  @override
  String selectedCount(int count) {
    return '已選擇 $count 項';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '已選擇 $count/$total 項';
  }

  @override
  String get noFilesFound => '找不到檔案';

  @override
  String get download => '下載';

  @override
  String get downloading => '下載中...';

  @override
  String get imageSavedToGallery => '圖片已儲存至相簿';

  @override
  String get failedToSaveImage => '圖片儲存失敗';

  @override
  String get noImagesToSave => '沒有可儲存的圖片';

  @override
  String get imageIndexOutOfRange => '圖片索引超出範圍';

  @override
  String get noTranslationResultSavingOriginal => '該圖片沒有翻譯結果，將儲存原圖';

  @override
  String get imageContentNotFound => '無法取得圖片內容';

  @override
  String get imageDataGenerationFailed => '圖片資料生成失敗';

  @override
  String get imageSavedSuccessfully => '圖片儲存成功';

  @override
  String get savingFailed => '儲存失敗';

  @override
  String get originalImageSavedSuccessfully => '原圖儲存成功';

  @override
  String networkRequestFailed(String statusCode) {
    return '網路請求失敗: $statusCode';
  }

  @override
  String get cannotGetImageData => '無法取得圖片資料';

  @override
  String saveOriginalImageFailed(String error) {
    return '儲存原圖失敗: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext => '儲存翻譯圖片需要Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      '翻譯圖片儲存需要透過RepaintBoundary實現，請使用saveCurrentDisplayImage方法';

  @override
  String saveTranslatedImageFailed(String error) {
    return '儲存翻譯圖片失敗: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '成功儲存 $count 張圖片';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '成功儲存 $successCount 張，失敗 $failureCount 張';
  }

  @override
  String errorDownloading(String error) {
    return '下載錯誤：$error';
  }

  @override
  String loadingFailed(String error) {
    return '載入失敗 $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return '刪除 $count 項？';
  }

  @override
  String get drafts => '待處理任務';

  @override
  String toLanguage(String lang) {
    return '翻譯至：$lang';
  }

  @override
  String get translateAll => '全部翻譯';

  @override
  String get nothingSelected => '未選擇任何項目';

  @override
  String operationFailed(String error) {
    return '失敗：$error';
  }

  @override
  String get allJobsDone => '所有任務已完成';

  @override
  String get imageNotFound => '找不到圖片';

  @override
  String usesLeftToday(int count) {
    return '今日剩餘 $count 次使用';
  }

  @override
  String get upgradeToPro => '升級 PRO 享無限使用';

  @override
  String get upgradeNow => '立即升級';

  @override
  String get updatingTranslationPosition => '正在更新翻譯位置...';

  @override
  String get noImagesFound => '找不到圖片或正在延遲載入';

  @override
  String get localTranslationInitializing => '正在初始化本地翻譯服務...';

  @override
  String get localTranslationFailed => '本地翻譯失敗';

  @override
  String get ocrServiceNotInitialized => 'OCR服務未初始化';

  @override
  String get translationServiceNotInitialized => '翻譯服務未初始化';

  @override
  String get processingImagesForTranslation => '正在處理圖片進行翻譯...';

  @override
  String localTranslationCompleted(int count) {
    return '已完成 $count 張圖片的本地翻譯';
  }

  @override
  String get newTab => '新增分頁';

  @override
  String get addBookmark => '加入書籤';

  @override
  String get removeBookmark => '移除書籤';

  @override
  String get unableToProcessImages => '無法在頁面上找到可處理的圖片。';

  @override
  String downloadFailed(String error) {
    return '下載失敗：$error';
  }

  @override
  String get selectAtLeastOneImage => '請至少選擇一張圖片';

  @override
  String get noValidImagesToDownload => '沒有可下載的有效圖片';

  @override
  String failedToProcessImages(String error) {
    return '處理圖片失敗：$error';
  }

  @override
  String get loadingImages => '正在載入圖片...';

  @override
  String get deleteThisItem => '刪除此項？';

  @override
  String get errorLoadingImageViewer => '圖片檢視器載入錯誤';

  @override
  String get failedToDeleteImage => '刪除圖片失敗';

  @override
  String completedTranslationAt(String time) {
    return '翻譯完成於\n$time';
  }

  @override
  String get dailyLimitReached => '已達每日上限';

  @override
  String get quotaResetMessage => '免費次數明日重置，看廣告可額外獲得';

  @override
  String get upgradeButton => '升級';

  @override
  String get tryTomorrowButton => '明天再試';

  @override
  String get followSystem => '跟隨系統';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => '解鎖檔案翻譯';

  @override
  String get highQualityTranslation => '高品質翻譯';

  @override
  String get adFreeExperience => '無廣告體驗';

  @override
  String get getPro => '升級PRO';

  @override
  String get loadFailed => '載入失敗，請檢查網路連接';

  @override
  String get back => '返回';

  @override
  String get purchaseSuccessful => '購買成功';

  @override
  String get purchaseRestored => '購買已恢復';

  @override
  String restoreFailed(String error) {
    return '恢復失敗：$error';
  }

  @override
  String get weekly => '每週';

  @override
  String get annual => '每年';

  @override
  String get free => '免費';

  @override
  String get freeText => '3天免費試用';

  @override
  String get billedMonthly => '按月計費';

  @override
  String get billedAnnual => '按年計費';

  @override
  String get billedWeekly => '按週計費';

  @override
  String get freeTrialText => '3天免費試用';

  @override
  String get save30Percent => '節省30%';

  @override
  String get loginTitle => '登入到您的\n帳戶';

  @override
  String get watchAd => '領取更多';

  @override
  String get watchAdDescription => '觀看廣告以獲得更多使用次數';

  @override
  String get fontSettings => '字體設置';

  @override
  String get fontFamily => '字體系列';

  @override
  String get strokeSettings => '描邊設置';

  @override
  String get textStyleSettings => '文本樣式設置';

  @override
  String get resetToDefaults => '重置為默認設置';

  @override
  String get fontSize => '字體大小';

  @override
  String get fontWeight => '字體粗細';

  @override
  String get textColor => '文本顏色';

  @override
  String get textLabel => '文字';

  @override
  String get strokeLabel => '描邊';

  @override
  String get strokeWidth => '描邊寬度';

  @override
  String get shadowEffect => '陰影效果';

  @override
  String get opacity => '透明度';

  @override
  String get horizontalOffset => '水平偏移';

  @override
  String get verticalOffset => '垂直偏移';

  @override
  String get blurRadius => '模糊半徑';

  @override
  String get fontWeightThin => '細體';

  @override
  String get fontWeightNormal => '正常';

  @override
  String get fontWeightMedium => '中等';

  @override
  String get fontWeightSemiBold => '半粗';

  @override
  String get fontWeightBold => '粗體';

  @override
  String get fontWeightExtraBold => '特粗';

  @override
  String get fontWeightBlack => '超粗';

  @override
  String get recommendedWebsites => '推薦網站';

  @override
  String get myBookmarks => '我的收藏';

  @override
  String get noBookmarks => '暫無收藏';

  @override
  String get bookmarkAdded => '已加入收藏';

  @override
  String get alreadyBookmarked => '已收藏此頁面';

  @override
  String get cannotBookmarkEmptyPage => '無法收藏空白頁面';

  @override
  String get bookmarkFailed => '收藏失敗';

  @override
  String get deleteBookmark => '刪除書籤';

  @override
  String deleteBookmarkConfirm(String title) {
    return '確定要刪除書籤 \"$title\" 嗎？';
  }

  @override
  String get bookmarkDeleted => '書籤已刪除';

  @override
  String get manageBookmarks => '管理';

  @override
  String get allBookmarks => '全部收藏';

  @override
  String get browsingHistory => '瀏覽記錄';

  @override
  String get noBrowsingHistory => '暫無瀏覽記錄';

  @override
  String get deleteHistory => '刪除記錄';

  @override
  String get deleteHistoryConfirm => '確定要刪除此瀏覽記錄嗎？';

  @override
  String get historyDeleted => '瀏覽記錄已刪除';

  @override
  String get justNow => '剛剛';

  @override
  String minutesAgo(int count) {
    return '$count分鐘前';
  }

  @override
  String hoursAgo(int count) {
    return '$count小時前';
  }

  @override
  String daysAgo(int count) {
    return '$count天前';
  }

  @override
  String downloadingLanguageFiles(String languageName) {
    return '正在下載語言檔案 ($languageName)';
  }

  @override
  String get translationInProgress => '翻譯進行中...';

  @override
  String get translateThisImage => '翻譯此圖片';

  @override
  String get imageTranslationLoading => '正在翻譯圖片...';
}
