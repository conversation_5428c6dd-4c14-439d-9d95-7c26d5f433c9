// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => 'Traductor de imágenes';

  @override
  String get targetLanguage => 'Idioma destino';

  @override
  String get language => 'Idioma';

  @override
  String get displayLanguage => 'Idioma de visualización';

  @override
  String get darkMode => 'Modo oscuro';

  @override
  String get cancel => 'Cancelar';

  @override
  String get confirm => 'Confirmar';

  @override
  String get ok => 'OK';

  @override
  String get delete => 'Eliminar';

  @override
  String get rename => 'Renombrar';

  @override
  String get loading => 'Cargando...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Éxito';

  @override
  String get retry => 'Reintentar';

  @override
  String get account => 'Cuenta';

  @override
  String get feedback => 'Comentarios';

  @override
  String get about => 'Acerca de';

  @override
  String get signOut => 'Cerrar sesión';

  @override
  String get translate => 'Traducir';

  @override
  String get translating => 'Traduciendo';

  @override
  String get translated => 'Traducido';

  @override
  String get failed => 'Operación fallida';

  @override
  String get selectTargetLanguage => 'Traducir por defecto a';

  @override
  String get targetLanguageDescription =>
      'El idioma seleccionado a continuación se utilizará como idioma de destino predeterminado para la traducción.';

  @override
  String get darkModeDescription =>
      'Elija su modo de tema preferido para la aplicación.';

  @override
  String get selectTheme => 'Seleccionar tema';

  @override
  String get appLanguageSetting => 'Idioma';

  @override
  String get contactUs => 'Contáctenos';

  @override
  String get appDescription =>
      'Traducción de manga e imágenes de alta calidad\ncon superposición de texto perfecta';

  @override
  String get feedbackHint => 'Sus comentarios nos ayudan a mejorar';

  @override
  String get emailHint => 'Correo electrónico (opcional)';

  @override
  String get send => 'Enviar';

  @override
  String get deleteData => 'Eliminar datos';

  @override
  String get deleteDataWarningTitle => '¿Eliminar datos?';

  @override
  String get deleteDataWarningText =>
      'Su cuenta y todos los datos serán eliminados. Esta acción no se puede deshacer.';

  @override
  String get done => 'Hecho';

  @override
  String get deleteDataSuccess => 'Sus datos han sido eliminados por completo.';

  @override
  String get signIn => 'Iniciar sesión';

  @override
  String get browserSettings => 'Configuración del navegador';

  @override
  String get adBlocking => 'Bloqueo de anuncios';

  @override
  String get adBlockingDescription =>
      'Bloquear anuncios y rastreadores mientras navega';

  @override
  String get adBlockingEnabled => 'Bloqueo de anuncios activado';

  @override
  String get adBlockingDisabled => 'Bloqueo de anuncios desactivado';

  @override
  String get enterUrl => 'Ingrese URL o palabras clave de búsqueda';

  @override
  String get errorLoadingImage => 'Error al cargar la imagen';

  @override
  String get selectImages => 'Por favor seleccione al menos una imagen';

  @override
  String get noValidImages => 'No hay imágenes válidas para descargar';

  @override
  String processingError(String error) {
    return 'Error al procesar imágenes: $error';
  }

  @override
  String get autoRenew => 'Renovación automática, cancele en cualquier momento';

  @override
  String get privacyPolicy => 'Política de privacidad';

  @override
  String get userAgreement => 'Acuerdo de usuario';

  @override
  String get operationTakingLong =>
      'La operación está tardando más de lo esperado';

  @override
  String get system => 'Sistema';

  @override
  String get light => 'Claro';

  @override
  String get dark => 'Oscuro';

  @override
  String get chinese => 'Chino';

  @override
  String get english => 'Inglés';

  @override
  String get japanese => 'Japonés';

  @override
  String get korean => 'Coreano';

  @override
  String get french => 'Francés';

  @override
  String get german => 'Alemán';

  @override
  String get spanish => 'Español';

  @override
  String get italian => 'Italiano';

  @override
  String get thai => 'Tailandés';

  @override
  String get vietnamese => 'Vietnamita';

  @override
  String get indonesian => 'Indonesio';

  @override
  String get malay => 'Malayo';

  @override
  String get feedbackSuccess => '¡Gracias por tus comentarios!';

  @override
  String get feedbackError =>
      'Error al enviar comentarios. Por favor, inténtelo de nuevo.';

  @override
  String get feedbackEmpty => 'Lo sentimos, pero el contenido está vacío';

  @override
  String get feedbackSendError => 'Error al enviar comentarios';

  @override
  String get generalError =>
      'Se ha producido un error. Por favor, inténtelo de nuevo.';

  @override
  String get deleteAccount => 'Eliminar cuenta';

  @override
  String get deleteAccountWarning =>
      'Esta acción no se puede deshacer. Todos sus datos serán eliminados permanentemente.';

  @override
  String get confirmDelete => 'Confirmar eliminación';

  @override
  String get deleteSuccess => 'Cuenta eliminada con éxito';

  @override
  String get deleteError =>
      'Error al eliminar la cuenta. Por favor, inténtelo de nuevo.';

  @override
  String get subscriptionDescription =>
      'Renovación automática, cancele en cualquier momento';

  @override
  String get subscribe => 'Suscribirse';

  @override
  String get restore => 'Restaurar';

  @override
  String get termsOfService => 'Términos de servicio';

  @override
  String get monthly => 'Mensual';

  @override
  String get mostPopular => 'Más popular';

  @override
  String get bestValue => 'Mejor valor';

  @override
  String get unlimitedManga => 'Generación ilimitada de manga';

  @override
  String get highQuality => 'Imágenes de alta calidad';

  @override
  String get prioritySupport => 'Soporte prioritario';

  @override
  String get noAds => 'Sin anuncios';

  @override
  String get popular => 'Popular';

  @override
  String get freeTrial => 'Prueba gratuita';

  @override
  String freeTrialDescription(String price) {
    return 'Disfrute de una prueba gratuita de 3 días, luego $price semanal';
  }

  @override
  String get fileUploadFeature =>
      'Soporte para carga de archivos para traducción';

  @override
  String get higherResolutionFeature => 'Mayor resolución';

  @override
  String get accurateTranslationFeature => 'Traducción más precisa';

  @override
  String get unlimitedTranslationsFeature =>
      'Traducciones de imágenes diarias ilimitadas';

  @override
  String get adFreeFeature => 'Experiencia sin anuncios';

  @override
  String get accountError => 'Error de cuenta';

  @override
  String get noProductsError => 'No hay productos disponibles';

  @override
  String get processing => 'Procesando...';

  @override
  String get purchaseFailed => 'Error en la compra';

  @override
  String get restoring => 'Restaurando compras...';

  @override
  String get tryAgainLater => 'Por favor, inténtelo de nuevo más tarde';

  @override
  String get networkError => 'Error de red';

  @override
  String get importFile => 'Importar archivo';

  @override
  String get album => 'Álbum';

  @override
  String get camera => 'Cámara';

  @override
  String get translateWebImages => 'Traducir imágenes web';

  @override
  String get recentTranslations => 'Traducciones recientes';

  @override
  String get seeAll => 'Ver todo';

  @override
  String get noTranslationHistory => 'Aún no hay historial de traducciones';

  @override
  String get allFiles => 'Todos los archivos';

  @override
  String get viewMode => 'Modo de vista';

  @override
  String get listMode => 'Modo lista';

  @override
  String get smallGridMode => 'Modo cuadrícula pequeña';

  @override
  String get largeGridMode => 'Modo cuadrícula grande';

  @override
  String get listModeDescription => 'Visualización de lista compacta';

  @override
  String get smallGridModeDescription => 'Visualización en cuadrícula pequeña';

  @override
  String get largeGridModeDescription => 'Visualización en cascada grande';

  @override
  String get singleImage => 'Imagen única';

  @override
  String multipleImages(int count) {
    return '$count imágenes';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count';
  }

  @override
  String selectedCount(int count) {
    return '$count seleccionado(s)';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '$count/$total seleccionado(s)';
  }

  @override
  String get noFilesFound => 'No se encontraron archivos';

  @override
  String get download => 'Descargar';

  @override
  String get downloading => 'Descargando...';

  @override
  String get imageSavedToGallery => 'Imagen guardada en la galería';

  @override
  String get failedToSaveImage => 'Error al guardar la imagen';

  @override
  String get noImagesToSave => 'No hay imágenes para guardar';

  @override
  String get imageIndexOutOfRange => 'Índice de imagen fuera de rango';

  @override
  String get noTranslationResultSavingOriginal =>
      'Sin resultado de traducción, guardando imagen original';

  @override
  String get imageContentNotFound =>
      'No se puede obtener el contenido de la imagen';

  @override
  String get imageDataGenerationFailed =>
      'Error en la generación de datos de imagen';

  @override
  String get imageSavedSuccessfully => 'Imagen guardada exitosamente';

  @override
  String get savingFailed => 'Error al guardar';

  @override
  String get originalImageSavedSuccessfully =>
      'Imagen original guardada exitosamente';

  @override
  String networkRequestFailed(String statusCode) {
    return 'Error en solicitud de red: $statusCode';
  }

  @override
  String get cannotGetImageData =>
      'No se pueden obtener los datos de la imagen';

  @override
  String saveOriginalImageFailed(String error) {
    return 'Error al guardar imagen original: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext =>
      'Guardar imagen traducida requiere Context';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      'Guardar imágenes traducidas requiere implementación de RepaintBoundary, use el método saveCurrentDisplayImage';

  @override
  String saveTranslatedImageFailed(String error) {
    return 'Error al guardar imagen traducida: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count imágenes guardadas exitosamente';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount guardadas exitosamente, $failureCount fallaron';
  }

  @override
  String errorDownloading(String error) {
    return 'Error al descargar: $error';
  }

  @override
  String loadingFailed(String error) {
    return 'Error al cargar $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return '¿Eliminar $count elemento(s)?';
  }

  @override
  String get drafts => 'Tareas pendientes';

  @override
  String toLanguage(String lang) {
    return 'A: $lang';
  }

  @override
  String get translateAll => 'Traducir todo';

  @override
  String get nothingSelected => 'Nada seleccionado';

  @override
  String operationFailed(String error) {
    return 'Falló: $error';
  }

  @override
  String get allJobsDone => 'Todos los trabajos terminados';

  @override
  String get imageNotFound => 'Imagen no encontrada';

  @override
  String usesLeftToday(int count) {
    return '$count usos hoy restantes';
  }

  @override
  String get upgradeToPro => 'Actualice a PRO para acceso ilimitado';

  @override
  String get upgradeNow => 'OBTENER PRO';

  @override
  String get updatingTranslationPosition =>
      'Actualizando posición de traducción...';

  @override
  String get noImagesFound =>
      'No se encontraron imágenes o carga diferida en progreso';

  @override
  String get localTranslationInitializing =>
      'Inicializando servicios de traducción local...';

  @override
  String get localTranslationFailed => 'Traducción local falló';

  @override
  String get ocrServiceNotInitialized => 'Servicio OCR no inicializado';

  @override
  String get translationServiceNotInitialized =>
      'Servicio de traducción no inicializado';

  @override
  String get processingImagesForTranslation =>
      'Procesando imágenes para traducción...';

  @override
  String localTranslationCompleted(int count) {
    return 'Traducción local completada para $count imágenes';
  }

  @override
  String get newTab => 'Nueva pestaña';

  @override
  String get addBookmark => 'Añadir marcador';

  @override
  String get removeBookmark => 'Eliminar marcador';

  @override
  String get unableToProcessImages =>
      'No se pudieron encontrar imágenes para procesar en la página.';

  @override
  String downloadFailed(String error) {
    return 'Descarga fallida: $error';
  }

  @override
  String get selectAtLeastOneImage =>
      'Por favor seleccione al menos una imagen';

  @override
  String get noValidImagesToDownload =>
      'No hay imágenes válidas para descargar';

  @override
  String failedToProcessImages(String error) {
    return 'Error al procesar imágenes: $error';
  }

  @override
  String get loadingImages => 'Cargando imágenes...';

  @override
  String get deleteThisItem => '¿Eliminar este elemento?';

  @override
  String get errorLoadingImageViewer => 'Error al cargar el visor de imágenes';

  @override
  String get failedToDeleteImage => 'Error al eliminar la imagen';

  @override
  String completedTranslationAt(String time) {
    return 'Traducción completada el \n$time';
  }

  @override
  String get dailyLimitReached => 'Límite diario alcanzado';

  @override
  String get quotaResetMessage =>
      'Límite gratis se renueva mañana. Mira anuncios para obtener más';

  @override
  String get upgradeButton => 'Actualizar';

  @override
  String get tryTomorrowButton => 'Intentar mañana';

  @override
  String get followSystem => 'Seguir sistema';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => 'Desbloquear traducción de archivos';

  @override
  String get highQualityTranslation => 'Traducción de alta calidad';

  @override
  String get adFreeExperience => 'Experiencia sin anuncios';

  @override
  String get getPro => 'OBTENER PRO';

  @override
  String get loadFailed =>
      'Carga fallida, por favor verifica tu conexión de red';

  @override
  String get back => 'Volver';

  @override
  String get purchaseSuccessful => 'Compra exitosa';

  @override
  String get purchaseRestored => 'Compra restaurada';

  @override
  String restoreFailed(String error) {
    return 'Restauración fallida: $error';
  }

  @override
  String get weekly => 'Semanal';

  @override
  String get annual => 'Anual';

  @override
  String get free => 'GRATIS';

  @override
  String get freeText => 'Prueba gratuita de 3 días';

  @override
  String get billedMonthly => 'Facturación mensual';

  @override
  String get billedAnnual => 'Facturación anual';

  @override
  String get billedWeekly => 'Facturación semanal';

  @override
  String get freeTrialText => 'Prueba gratuita de 3 días';

  @override
  String get save30Percent => 'Ahorra 30%';

  @override
  String get loginTitle => 'Iniciar sesión en\n su cuenta';

  @override
  String get watchAd => 'Ver anuncio';

  @override
  String get watchAdDescription =>
      'Mira un anuncio para obtener más cuota de uso';

  @override
  String get fontSettings => 'Configuración de fuente';

  @override
  String get fontFamily => 'Familia de fuente';

  @override
  String get strokeSettings => 'Configuración de contorno';

  @override
  String get textStyleSettings => 'Configuración de estilo de texto';

  @override
  String get resetToDefaults => 'Restablecer a valores predeterminados';

  @override
  String get fontSize => 'Tamaño de fuente';

  @override
  String get fontWeight => 'Peso de fuente';

  @override
  String get textColor => 'Color del texto';

  @override
  String get textLabel => 'Texto';

  @override
  String get strokeLabel => 'Contorno';

  @override
  String get strokeWidth => 'Ancho del contorno';

  @override
  String get shadowEffect => 'Efecto de sombra';

  @override
  String get opacity => 'Opacidad';

  @override
  String get horizontalOffset => 'Desplazamiento horizontal';

  @override
  String get verticalOffset => 'Desplazamiento vertical';

  @override
  String get blurRadius => 'Radio de desenfoque';

  @override
  String get fontWeightThin => 'Fino';

  @override
  String get fontWeightNormal => 'Normal';

  @override
  String get fontWeightMedium => 'Medio';

  @override
  String get fontWeightSemiBold => 'Semi-negrita';

  @override
  String get fontWeightBold => 'Negrita';

  @override
  String get fontWeightExtraBold => 'Extra-negrita';

  @override
  String get fontWeightBlack => 'Negro';

  @override
  String get recommendedWebsites => 'Sitios web recomendados';

  @override
  String get myBookmarks => 'Mis marcadores';

  @override
  String get noBookmarks => 'Sin marcadores';

  @override
  String get bookmarkAdded => 'Marcador añadido';

  @override
  String get alreadyBookmarked => 'Ya está marcado';

  @override
  String get cannotBookmarkEmptyPage => 'No se puede marcar una página vacía';

  @override
  String get bookmarkFailed => 'Error al añadir marcador';

  @override
  String get deleteBookmark => 'Eliminar marcador';

  @override
  String deleteBookmarkConfirm(String title) {
    return '¿Está seguro de que desea eliminar el marcador \"$title\"?';
  }

  @override
  String get bookmarkDeleted => 'Marcador eliminado';

  @override
  String get manageBookmarks => 'Gestionar';

  @override
  String get allBookmarks => 'Todos los marcadores';

  @override
  String get browsingHistory => 'Historial';

  @override
  String get noBrowsingHistory => 'Aún no hay historial de navegación';

  @override
  String get deleteHistory => 'Eliminar historial';

  @override
  String get deleteHistoryConfirm =>
      '¿Está seguro de que desea eliminar este elemento del historial?';

  @override
  String get historyDeleted => 'Elemento del historial eliminado';

  @override
  String get justNow => 'Ahora mismo';

  @override
  String minutesAgo(int count) {
    return 'hace $count minutos';
  }

  @override
  String hoursAgo(int count) {
    return 'hace $count horas';
  }

  @override
  String daysAgo(int count) {
    return 'hace $count días';
  }

  @override
  String downloadingLanguageFiles(String languageName) {
    return 'Descargando archivos de idioma ($languageName)';
  }

  @override
  String get translationInProgress => 'Traducción en progreso...';

  @override
  String get translateThisImage => 'Traducir esta imagen';

  @override
  String get imageTranslationLoading => 'Traduciendo imagen...';

  @override
  String get downloadTimeout =>
      'Tiempo de espera de descarga agotado, por favor verifique su conexión de red';
}
