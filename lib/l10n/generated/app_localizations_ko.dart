// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appName => 'Imtrans';

  @override
  String get appSubtitle => '이미지 번역기';

  @override
  String get targetLanguage => '대상 언어';

  @override
  String get language => '언어';

  @override
  String get displayLanguage => '표시 언어';

  @override
  String get darkMode => '다크 모드';

  @override
  String get cancel => '취소';

  @override
  String get confirm => '확인';

  @override
  String get ok => '확인';

  @override
  String get delete => '삭제';

  @override
  String get rename => '이름 변경';

  @override
  String get loading => '로딩 중..';

  @override
  String get error => '오류';

  @override
  String get success => '성공';

  @override
  String get retry => '다시 시도';

  @override
  String get account => '계정';

  @override
  String get feedback => '피드백';

  @override
  String get about => '정보';

  @override
  String get signOut => '로그아웃';

  @override
  String get translate => '번역';

  @override
  String get translating => '번역 중';

  @override
  String get translated => '번역됨';

  @override
  String get failed => '작업 실패';

  @override
  String get selectTargetLanguage => '기본 번역 대상';

  @override
  String get targetLanguageDescription => '아래에서 선택한 언어가 번역의 기본 대상 언어로 사용됩니다.';

  @override
  String get darkModeDescription => '앱의 선호하는 테마 모드를 선택하세요.';

  @override
  String get selectTheme => '테마 선택';

  @override
  String get appLanguageSetting => '언어';

  @override
  String get contactUs => '문의하기';

  @override
  String get appDescription => '고품질 만화 및 이미지 번역\n원활한 텍스트 오버레이';

  @override
  String get feedbackHint => '귀하의 피드백은 개선에 도움이 됩니다';

  @override
  String get emailHint => '이메일 (선택사항)';

  @override
  String get send => '보내기';

  @override
  String get deleteData => '데이터 삭제';

  @override
  String get deleteDataWarningTitle => '데이터 삭제?';

  @override
  String get deleteDataWarningText => '계정과 모든 데이터가 삭제됩니다. 이 작업은 취소할 수 없습니다.';

  @override
  String get done => '완료';

  @override
  String get deleteDataSuccess => '데이터가 완전히 삭제되었습니다.';

  @override
  String get signIn => '로그인';

  @override
  String get browserSettings => '브라우저 설정';

  @override
  String get adBlocking => '광고 차단';

  @override
  String get adBlockingDescription => '브라우징 중 광고 및 추적기 차단';

  @override
  String get adBlockingEnabled => '광고 차단 활성화됨';

  @override
  String get adBlockingDisabled => '광고 차단 비활성화됨';

  @override
  String get enterUrl => 'URL 또는 검색 키워드 입력';

  @override
  String get errorLoadingImage => '이미지 로딩 오류';

  @override
  String get selectImages => '하나 이상의 이미지를 선택하세요';

  @override
  String get noValidImages => '다운로드할 유효한 이미지가 없습니다';

  @override
  String processingError(String error) {
    return '이미지 처리 실패: $error';
  }

  @override
  String get autoRenew => '자동 갱신, 언제든지 취소 가능';

  @override
  String get privacyPolicy => '개인정보 처리방침';

  @override
  String get userAgreement => '사용자 동의';

  @override
  String get operationTakingLong => '작업이 예상보다 오래 걸리고 있습니다';

  @override
  String get system => '시스템';

  @override
  String get light => '라이트';

  @override
  String get dark => '다크';

  @override
  String get chinese => '중국어';

  @override
  String get english => '영어';

  @override
  String get japanese => '일본어';

  @override
  String get korean => '한국어';

  @override
  String get french => '프랑스어';

  @override
  String get german => '독일어';

  @override
  String get spanish => '스페인어';

  @override
  String get italian => '이탈리아어';

  @override
  String get thai => '태국어';

  @override
  String get vietnamese => '베트남어';

  @override
  String get indonesian => '인도네시아어';

  @override
  String get malay => '말레이어';

  @override
  String get feedbackSuccess => '피드백 감사합니다!';

  @override
  String get feedbackError => '피드백 제출에 실패했습니다. 다시 시도해 주세요.';

  @override
  String get feedbackEmpty => '죄송합니다만 내용이 비어 있습니다';

  @override
  String get feedbackSendError => '피드백 전송에 실패했습니다';

  @override
  String get generalError => '오류가 발생했습니다. 다시 시도해 주세요.';

  @override
  String get deleteAccount => '계정 삭제';

  @override
  String get deleteAccountWarning => '이 작업은 취소할 수 없습니다. 모든 데이터가 영구적으로 삭제됩니다.';

  @override
  String get confirmDelete => '삭제 확인';

  @override
  String get deleteSuccess => '계정이 성공적으로 삭제되었습니다';

  @override
  String get deleteError => '계정 삭제에 실패했습니다. 다시 시도해 주세요.';

  @override
  String get subscriptionDescription => '자동 갱신, 언제든지 취소 가능';

  @override
  String get subscribe => '구독하기';

  @override
  String get restore => '복원';

  @override
  String get termsOfService => '서비스 약관';

  @override
  String get monthly => '월간';

  @override
  String get mostPopular => '가장 인기';

  @override
  String get bestValue => '최고 가치';

  @override
  String get unlimitedManga => '무제한 만화 생성';

  @override
  String get highQuality => '고품질 이미지';

  @override
  String get prioritySupport => '우선 지원';

  @override
  String get noAds => '광고 없음';

  @override
  String get popular => '인기';

  @override
  String get freeTrial => '무료 체험';

  @override
  String freeTrialDescription(String price) {
    return '3일 무료 체험, 이후 주$price';
  }

  @override
  String get fileUploadFeature => '파일 업로드 번역 지원';

  @override
  String get higherResolutionFeature => '고해상도';

  @override
  String get accurateTranslationFeature => '더 정확한 번역';

  @override
  String get unlimitedTranslationsFeature => '일일 무제한 이미지 번역';

  @override
  String get adFreeFeature => '광고 없는 경험';

  @override
  String get accountError => '계정 오류';

  @override
  String get noProductsError => '이용 가능한 상품이 없습니다';

  @override
  String get processing => '처리 중...';

  @override
  String get purchaseFailed => '구매 실패';

  @override
  String get restoring => '구매 복원 중...';

  @override
  String get tryAgainLater => '나중에 다시 시도해 주세요';

  @override
  String get networkError => '네트워크 오류';

  @override
  String get importFile => '파일 가져오기';

  @override
  String get album => '앨범';

  @override
  String get camera => '카메라';

  @override
  String get translateWebImages => '웹 이미지 번역';

  @override
  String get recentTranslations => '최근 번역';

  @override
  String get seeAll => '모두 보기';

  @override
  String get noTranslationHistory => '아직 번역 기록이 없습니다';

  @override
  String get allFiles => '모든 파일';

  @override
  String get viewMode => '보기 방식';

  @override
  String get listMode => '목록 모드';

  @override
  String get smallGridMode => '작은 그리드 모드';

  @override
  String get largeGridMode => '큰 그리드 모드';

  @override
  String get listModeDescription => '간결한 목록 표시';

  @override
  String get smallGridModeDescription => '작은 크기 그리드 표시';

  @override
  String get largeGridModeDescription => '큰 크기 워터폴 표시';

  @override
  String get singleImage => '단일 이미지';

  @override
  String multipleImages(int count) {
    return '$count개 이미지';
  }

  @override
  String multipleImagesShort(int count) {
    return '$count개';
  }

  @override
  String selectedCount(int count) {
    return '$count개 선택됨';
  }

  @override
  String selectedCountWithTotal(int count, int total) {
    return '$count/$total개 선택됨';
  }

  @override
  String get noFilesFound => '파일을 찾을 수 없습니다';

  @override
  String get download => '다운로드';

  @override
  String get downloading => '다운로드 중...';

  @override
  String get imageSavedToGallery => '이미지가 갤러리에 저장되었습니다';

  @override
  String get failedToSaveImage => '이미지 저장 실패';

  @override
  String get noImagesToSave => '저장할 이미지가 없습니다';

  @override
  String get imageIndexOutOfRange => '이미지 인덱스가 범위를 벗어났습니다';

  @override
  String get noTranslationResultSavingOriginal => '번역 결과가 없어 원본 이미지를 저장합니다';

  @override
  String get imageContentNotFound => '이미지 콘텐츠를 가져올 수 없습니다';

  @override
  String get imageDataGenerationFailed => '이미지 데이터 생성 실패';

  @override
  String get imageSavedSuccessfully => '이미지 저장 성공';

  @override
  String get savingFailed => '저장 실패';

  @override
  String get originalImageSavedSuccessfully => '원본 이미지 저장 성공';

  @override
  String networkRequestFailed(String statusCode) {
    return '네트워크 요청 실패: $statusCode';
  }

  @override
  String get cannotGetImageData => '이미지 데이터를 가져올 수 없습니다';

  @override
  String saveOriginalImageFailed(String error) {
    return '원본 이미지 저장 실패: $error';
  }

  @override
  String get saveTranslatedImageNeedsContext => '번역된 이미지 저장에는 Context가 필요합니다';

  @override
  String get saveTranslatedImageUseRepaintBoundary =>
      '번역된 이미지 저장에는 RepaintBoundary 구현이 필요합니다. saveCurrentDisplayImage 메서드를 사용하세요';

  @override
  String saveTranslatedImageFailed(String error) {
    return '번역된 이미지 저장 실패: $error';
  }

  @override
  String savedSuccessfully(int count) {
    return '$count개 이미지 저장 성공';
  }

  @override
  String savedPartially(int successCount, int failureCount) {
    return '$successCount개 성공, $failureCount개 실패';
  }

  @override
  String errorDownloading(String error) {
    return '다운로드 오류: $error';
  }

  @override
  String loadingFailed(String error) {
    return '로딩 실패 $error';
  }

  @override
  String deleteConfirmation(int count, String s) {
    return '$count개 항목을 삭제하시겠습니까?';
  }

  @override
  String get drafts => '대기 중인 작업';

  @override
  String toLanguage(String lang) {
    return '대상: $lang';
  }

  @override
  String get translateAll => '모두 번역';

  @override
  String get nothingSelected => '선택된 항목 없음';

  @override
  String operationFailed(String error) {
    return '실패: $error';
  }

  @override
  String get allJobsDone => '모든 작업 완료';

  @override
  String get imageNotFound => '이미지를 찾을 수 없습니다';

  @override
  String usesLeftToday(int count) {
    return '오늘 남은 $count회';
  }

  @override
  String get upgradeToPro => '무제한 액세스를 위해 PRO로 업그레이드';

  @override
  String get upgradeNow => '획득 PRO';

  @override
  String get updatingTranslationPosition => '번역 위치 업데이트 중...';

  @override
  String get noImagesFound => '이미지를 찾을 수 없거나 지연 로딩 중';

  @override
  String get localTranslationInitializing => '로컬 번역 서비스 초기화 중...';

  @override
  String get localTranslationFailed => '로컬 번역 실패';

  @override
  String get ocrServiceNotInitialized => 'OCR 서비스가 초기화되지 않음';

  @override
  String get translationServiceNotInitialized => '번역 서비스가 초기화되지 않음';

  @override
  String get processingImagesForTranslation => '번역을 위해 이미지 처리 중...';

  @override
  String localTranslationCompleted(int count) {
    return '$count개 이미지의 로컬 번역 완료';
  }

  @override
  String get newTab => '새 탭';

  @override
  String get addBookmark => '북마크 추가';

  @override
  String get removeBookmark => '북마크 제거';

  @override
  String get unableToProcessImages => '페이지에서 처리할 이미지를 찾을 수 없습니다.';

  @override
  String downloadFailed(String error) {
    return '다운로드 실패: $error';
  }

  @override
  String get selectAtLeastOneImage => '하나 이상의 이미지를 선택하세요';

  @override
  String get noValidImagesToDownload => '다운로드할 유효한 이미지가 없습니다';

  @override
  String failedToProcessImages(String error) {
    return '이미지 처리 실패: $error';
  }

  @override
  String get loadingImages => '이미지 로딩 중...';

  @override
  String get deleteThisItem => '이 항목을 삭제하시겠습니까?';

  @override
  String get errorLoadingImageViewer => '이미지 뷰어 로딩 오류';

  @override
  String get failedToDeleteImage => '이미지 삭제 실패';

  @override
  String completedTranslationAt(String time) {
    return '번역 완료 시간: \n$time';
  }

  @override
  String get dailyLimitReached => '일일 한도 도달';

  @override
  String get quotaResetMessage => '무료 이용 횟수 내일 초기화. 광고 보면 더 줍니다';

  @override
  String get upgradeButton => '업그레이드';

  @override
  String get tryTomorrowButton => '내일 다시 시도';

  @override
  String get followSystem => '시스템 설정 따르기';

  @override
  String get proTitle => 'PRO';

  @override
  String get unlockFileUpload => '파일 번역 잠금 해제';

  @override
  String get highQualityTranslation => '고품질 번역';

  @override
  String get adFreeExperience => '광고 없는 경험';

  @override
  String get getPro => 'PRO 구매';

  @override
  String get loadFailed => '로딩 실패, 네트워크 연결을 확인하세요';

  @override
  String get back => '뒤로';

  @override
  String get purchaseSuccessful => '구매 성공';

  @override
  String get purchaseRestored => '구매 복원됨';

  @override
  String restoreFailed(String error) {
    return '복원 실패: $error';
  }

  @override
  String get weekly => '주간';

  @override
  String get annual => '연간';

  @override
  String get free => '무료';

  @override
  String get freeText => '3일 무료 체험';

  @override
  String get billedMonthly => '월간 청구';

  @override
  String get billedAnnual => '연간 청구';

  @override
  String get billedWeekly => '주간 청구';

  @override
  String get freeTrialText => '3일 무료 체험';

  @override
  String get save30Percent => '30% 절약';

  @override
  String get loginTitle => '계정에\n 로그인';

  @override
  String get watchAd => '더 받기';

  @override
  String get watchAdDescription => '광고를 시청하여 더 많은 사용 횟수 얻기';

  @override
  String get fontSettings => '글꼴 설정';

  @override
  String get fontFamily => '글꼴 패밀리';

  @override
  String get strokeSettings => '윤곽선 설정';

  @override
  String get textStyleSettings => '텍스트 스타일 설정';

  @override
  String get resetToDefaults => '기본값으로 재설정';

  @override
  String get fontSize => '글꼴 크기';

  @override
  String get fontWeight => '글꼴 굵기';

  @override
  String get textColor => '텍스트 색상';

  @override
  String get textLabel => '텍스트';

  @override
  String get strokeLabel => '윤곽선';

  @override
  String get strokeWidth => '윤곽선 두께';

  @override
  String get shadowEffect => '그림자 효과';

  @override
  String get opacity => '투명도';

  @override
  String get horizontalOffset => '수평 오프셋';

  @override
  String get verticalOffset => '수직 오프셋';

  @override
  String get blurRadius => '흐림 반경';

  @override
  String get fontWeightThin => '얇음';

  @override
  String get fontWeightNormal => '보통';

  @override
  String get fontWeightMedium => '중간';

  @override
  String get fontWeightSemiBold => '반굵음';

  @override
  String get fontWeightBold => '굵음';

  @override
  String get fontWeightExtraBold => '매우굵음';

  @override
  String get fontWeightBlack => '블랙';

  @override
  String get recommendedWebsites => '추천 웹사이트';

  @override
  String get myBookmarks => '내 북마크';

  @override
  String get noBookmarks => '북마크가 없습니다';

  @override
  String get bookmarkAdded => '북마크에 추가되었습니다';

  @override
  String get alreadyBookmarked => '이미 북마크되어 있습니다';

  @override
  String get cannotBookmarkEmptyPage => '빈 페이지는 북마크할 수 없습니다';

  @override
  String get bookmarkFailed => '북마크 추가에 실패했습니다';

  @override
  String get deleteBookmark => '북마크 삭제';

  @override
  String deleteBookmarkConfirm(String title) {
    return '북마크 \"$title\"을(를) 삭제하시겠습니까?';
  }

  @override
  String get bookmarkDeleted => '북마크가 삭제되었습니다';

  @override
  String get manageBookmarks => '관리';

  @override
  String get allBookmarks => '모든 북마크';

  @override
  String get browsingHistory => '방문 기록';

  @override
  String get noBrowsingHistory => '아직 방문 기록이 없습니다';

  @override
  String get deleteHistory => '기록 삭제';

  @override
  String get deleteHistoryConfirm => '이 방문 기록 항목을 삭제하시겠습니까?';

  @override
  String get historyDeleted => '방문 기록 항목이 삭제되었습니다';

  @override
  String get justNow => '방금 전';

  @override
  String minutesAgo(int count) {
    return '$count분 전';
  }

  @override
  String hoursAgo(int count) {
    return '$count시간 전';
  }

  @override
  String daysAgo(int count) {
    return '$count일 전';
  }

  @override
  String downloadingLanguageFiles(String languageName) {
    return '언어 파일 다운로드 중 ($languageName)';
  }

  @override
  String get translationInProgress => '번역 진행 중...';

  @override
  String get translateThisImage => '이 이미지 번역';

  @override
  String get imageTranslationLoading => '이미지 번역 중...';

  @override
  String get downloadTimeout => '다운로드 시간이 초과되었습니다. 네트워크 연결을 확인하세요.';
}
