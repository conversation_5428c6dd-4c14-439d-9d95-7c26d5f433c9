{"@@locale": "en", "appName": "Imtrans", "@appName": {"description": "App name"}, "appSubtitle": "Image Translator", "@appSubtitle": {"description": "App subtitle"}, "targetLanguage": "Target Language", "@targetLanguage": {"description": "Target language for translation"}, "language": "Language", "@language": {"description": "Language settings title"}, "displayLanguage": "Display Language", "@displayLanguage": {"description": "App interface language setting"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Dark mode toggle label"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "rename": "<PERSON><PERSON>", "@rename": {"description": "rename button text"}, "loading": "Loading..", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error message"}, "success": "Success", "@success": {"description": "Success message"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "account": "Account", "@account": {"description": "Account menu item"}, "feedback": "<PERSON><PERSON><PERSON>", "@feedback": {"description": "Feedback menu item"}, "about": "About", "@about": {"description": "About menu item"}, "signOut": "Sign Out", "@signOut": {"description": "Sign out button text"}, "translate": "Translate", "@translate": {"description": "Translate button text"}, "translating": "Translating", "@translating": {"description": "Translating status"}, "translated": "Translated", "@translated": {"description": "Translated status"}, "failed": "Operation failed", "@failed": {"description": "Failed status"}, "selectTargetLanguage": "De<PERSON>ult translate to", "@selectTargetLanguage": {"description": "Select target language title"}, "targetLanguageDescription": "The selected language below will be used as the default target language for translation.", "@targetLanguageDescription": {"description": "Target language selection description"}, "darkModeDescription": "Choose your preferred theme mode for the app.", "@darkModeDescription": {"description": "Dark mode setting description"}, "selectTheme": "Select Theme", "@selectTheme": {"description": "Theme selection title"}, "appLanguageSetting": "Language", "@appLanguageSetting": {"description": "App language setting"}, "contactUs": "Contact Us", "@contactUs": {"description": "Contact us page title"}, "appDescription": "High-quality Manga & Image Translation \nwith Seamless Text Overlay", "@appDescription": {"description": "App description text, used in user_drawer.dart and feedback.dart"}, "feedbackHint": "Your feedback helps us improve", "@feedbackHint": {"description": "Feedback input hint text"}, "emailHint": "Email (optional)", "@emailHint": {"description": "Email input hint text"}, "send": "Send", "@send": {"description": "Send button text"}, "deleteData": "Delete Data", "@deleteData": {"description": "Delete data button text"}, "deleteDataWarningTitle": "Delete Data?", "@deleteDataWarningTitle": {"description": "Delete data confirmation title"}, "deleteDataWarningText": "Your account and all data will be deleted. This action cannot be undone.", "@deleteDataWarningText": {"description": "Delete data warning message"}, "done": "Done", "@done": {"description": "Done"}, "deleteDataSuccess": "Your data has been completely deleted.", "@deleteDataSuccess": {"description": "Delete data success message"}, "signIn": "Sign In", "@signIn": {"description": "Sign in button text"}, "browserSettings": "Browser <PERSON>s", "@browserSettings": {"description": "Browser settings page title"}, "adBlocking": "Ad Blocking", "@adBlocking": {"description": "Ad blocking setting title"}, "adBlockingDescription": "Block ads and trackers while browsing", "@adBlockingDescription": {"description": "Ad blocking setting description"}, "adBlockingEnabled": "Ad blocking enabled", "@adBlockingEnabled": {"description": "Ad blocking enabled message"}, "adBlockingDisabled": "Ad blocking disabled", "@adBlockingDisabled": {"description": "Ad blocking disabled message"}, "enterUrl": "Enter URL or search keywords", "@enterUrl": {"description": "URL input hint text"}, "errorLoadingImage": "Error Loading Image", "@errorLoadingImage": {"description": "Error message when image fails to load, from text_overlay.dart"}, "selectImages": "Please select at least one image", "@selectImages": {"description": "Message when no images are selected, from download.dart"}, "noValidImages": "No valid images to download", "@noValidImages": {"description": "Message when there are no valid images, from download.dart"}, "processingError": "Failed to process images: {error}", "@processingError": {"description": "Error message when processing images fails, from download.dart", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "autoRenew": "Automatically renew, cancel at any time", "@autoRenew": {"description": "Auto-renewal notice, from purchase_view.dart"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy policy link text"}, "userAgreement": "User Agreement", "@userAgreement": {"description": "User agreement link text, from purchase_view.dart"}, "operationTakingLong": "Operation is taking longer than expected", "@operationTakingLong": {"description": "Message shown when operation takes too long, from loading.dart"}, "system": "System", "@system": {"description": "System theme option"}, "light": "Light", "@light": {"description": "Light theme option"}, "dark": "Dark", "@dark": {"description": "Dark theme option"}, "chinese": "Chinese", "@chinese": {"description": "chinese language name"}, "english": "English", "@english": {"description": "English language name"}, "japanese": "Japanese", "@japanese": {"description": "Japanese language name"}, "korean": "Korean", "@korean": {"description": "Korean language name"}, "french": "French", "@french": {"description": "French language name"}, "german": "German", "@german": {"description": "German language name"}, "spanish": "Spanish", "@spanish": {"description": "Spanish language name"}, "italian": "Italian", "@italian": {"description": "Italian language name"}, "thai": "Thai", "@thai": {"description": "Thai language name"}, "vietnamese": "Vietnamese", "@vietnamese": {"description": "Vietnamese language name"}, "indonesian": "Indonesian", "@indonesian": {"description": "Indonesian language name"}, "malay": "Malay", "@malay": {"description": "Malay language name"}, "feedbackSuccess": "Thank you for your feedback!", "@feedbackSuccess": {"description": "Feedback submission success message"}, "feedbackError": "Failed to submit feedback. Please try again.", "@feedbackError": {"description": "Feedback submission error message"}, "feedbackEmpty": "Sorry but the content is empty", "@feedbackEmpty": {"description": "Error message when feedback content is empty"}, "feedbackSendError": "Failed to send feedback", "@feedbackSendError": {"description": "Error message when sending feedback fails"}, "generalError": "An error occurred. Please try again.", "@generalError": {"description": "General error message"}, "deleteAccount": "Delete Account", "@deleteAccount": {"description": "Delete account button text"}, "deleteAccountWarning": "This action cannot be undone. All your data will be permanently deleted.", "@deleteAccountWarning": {"description": "Delete account warning message"}, "confirmDelete": "Confirm Delete", "@confirmDelete": {"description": "Confirm delete button text"}, "deleteSuccess": "Account deleted successfully", "@deleteSuccess": {"description": "Account deletion success message"}, "deleteError": "Failed to delete account. Please try again.", "@deleteError": {"description": "Account deletion error message"}, "subscriptionDescription": "Automatically renew, cancel at any time", "@subscriptionDescription": {"description": "Subscription auto-renewal description"}, "subscribe": "Subscribe", "@subscribe": {"description": "Subscribe button text"}, "restore": "Rest<PERSON>", "@restore": {"description": "Restore purchases button text"}, "termsOfService": "Terms of Service", "@termsOfService": {"description": "Terms of service link text"}, "monthly": "Monthly", "@monthly": {"description": "Monthly subscription option"}, "mostPopular": "Most Popular", "@mostPopular": {"description": "Most popular subscription option label"}, "bestValue": "Best Value", "@bestValue": {"description": "Best value subscription option label"}, "unlimitedManga": "Unlimited manga generation", "@unlimitedManga": {"description": "Unlimited manga generation feature"}, "highQuality": "High quality images", "@highQuality": {"description": "High quality images feature"}, "prioritySupport": "Priority support", "@prioritySupport": {"description": "Priority support feature"}, "noAds": "No ads", "@noAds": {"description": "No ads feature"}, "popular": "Popular", "@popular": {"description": "Popular subscription option label in purchase view"}, "freeTrial": "Free Trial", "@freeTrial": {"description": "Free trial button text in purchase view"}, "freeTrialDescription": "Enjoy 3-day free trial, then {price} weekly", "@freeTrialDescription": {"description": "Free trial description text in purchase view", "placeholders": {"price": {"type": "String", "example": "$9.99"}}}, "fileUploadFeature": "Supports File Upload For Translation", "@fileUploadFeature": {"description": "Feature description in purchase view"}, "higherResolutionFeature": "Higher Resolution", "@higherResolutionFeature": {"description": "Feature description in purchase view"}, "accurateTranslationFeature": "More Accurate Translation", "@accurateTranslationFeature": {"description": "Feature description in purchase view"}, "unlimitedTranslationsFeature": "Unlimited Daily Image Translations", "@unlimitedTranslationsFeature": {"description": "Feature description in purchase view"}, "adFreeFeature": "Ad-Free Experience", "@adFreeFeature": {"description": "Feature description in purchase view"}, "accountError": "Account error", "@accountError": {"description": "Account error message in purchase view"}, "noProductsError": "No products available", "@noProductsError": {"description": "No products error message in purchase view"}, "processing": "Processing...", "@processing": {"description": "Processing state message"}, "purchaseFailed": "Purchase failed", "@purchaseFailed": {"description": "Purchase failure message"}, "restoring": "Restoring purchases...", "@restoring": {"description": "Restoring purchases state message"}, "tryAgainLater": "Please try again later", "@tryAgainLater": {"description": "<PERSON><PERSON> later message"}, "networkError": "Network error", "@networkError": {"description": "Network error message"}, "importFile": "Import File", "@importFile": {"description": "Import file button label"}, "album": "Album", "@album": {"description": "Album button label"}, "camera": "Camera", "@camera": {"description": "Camera button label"}, "translateWebImages": "Translate Web Images", "@translateWebImages": {"description": "Web image translation section title"}, "recentTranslations": "Recent Translations", "@recentTranslations": {"description": "Recent translations section title"}, "seeAll": "See All", "@seeAll": {"description": "See all button text"}, "noTranslationHistory": "No translations yet", "@noTranslationHistory": {"description": "No translation history message"}, "allFiles": "All Files", "@allFiles": {"description": "Page title for the file list, from list.dart"}, "viewMode": "View Mode", "@viewMode": {"description": "View mode selection title, from list.dart"}, "listMode": "List Mode", "@listMode": {"description": "List view mode option, from list.dart"}, "smallGridMode": "Small Grid Mode", "@smallGridMode": {"description": "Small grid view mode option, from list.dart"}, "largeGridMode": "Large Grid Mode", "@largeGridMode": {"description": "Large grid view mode option, from list.dart"}, "listModeDescription": "Compact list display", "@listModeDescription": {"description": "List mode description, from list.dart"}, "smallGridModeDescription": "Small size grid display", "@smallGridModeDescription": {"description": "Small grid mode description, from list.dart"}, "largeGridModeDescription": "Large size waterfall display", "@largeGridModeDescription": {"description": "Large grid mode description, from list.dart"}, "singleImage": "Single image", "@singleImage": {"description": "Single image label, from list.dart"}, "multipleImages": "{count} images", "@multipleImages": {"description": "Multiple images label, from list.dart", "placeholders": {"count": {"type": "int", "example": "5"}}}, "multipleImagesShort": "{count}", "@multipleImagesShort": {"description": "Multiple images count only, from list.dart", "placeholders": {"count": {"type": "int", "example": "5"}}}, "selectedCount": "Selected {count}", "@selectedCount": {"description": "Title when items are selected, from list.dart", "placeholders": {"count": {"type": "int", "example": "3"}}}, "selectedCountWithTotal": "Selected {count}/{total}", "@selectedCountWithTotal": {"description": "Title when items are selected with total count, from list.dart", "placeholders": {"count": {"type": "int", "example": "3"}, "total": {"type": "int", "example": "10"}}}, "noFilesFound": "No files found", "@noFilesFound": {"description": "Message shown when no files are found, from list.dart"}, "download": "Download", "@download": {"description": "Download button text, from list.dart"}, "downloading": "Downloading..", "@downloading": {"description": "Download status message, from list.dart"}, "imageSavedToGallery": "Image saved to gallery", "@imageSavedToGallery": {"description": "Success message when image is saved to gallery, from list.dart"}, "failedToSaveImage": "Failed to save image", "@failedToSaveImage": {"description": "Error message when saving image fails, from list.dart"}, "noImagesToSave": "No images to save", "@noImagesToSave": {"description": "Error message when there are no images to save"}, "imageIndexOutOfRange": "Image index out of range", "@imageIndexOutOfRange": {"description": "Error message when image index is invalid"}, "noTranslationResultSavingOriginal": "No translation result, saving original image", "@noTranslationResultSavingOriginal": {"description": "Message when saving original image because no translation exists"}, "imageContentNotFound": "Cannot get image content", "@imageContentNotFound": {"description": "Error message when cannot capture image content"}, "imageDataGenerationFailed": "Image data generation failed", "@imageDataGenerationFailed": {"description": "Error message when image data generation fails"}, "imageSavedSuccessfully": "Image saved successfully", "@imageSavedSuccessfully": {"description": "Success message when image is saved"}, "savingFailed": "Saving failed", "@savingFailed": {"description": "Generic saving failed message"}, "originalImageSavedSuccessfully": "Original image saved successfully", "@originalImageSavedSuccessfully": {"description": "Success message when original image is saved"}, "networkRequestFailed": "Network request failed: {statusCode}", "@networkRequestFailed": {"description": "Error message when network request fails", "placeholders": {"statusCode": {"type": "String", "example": "404"}}}, "cannotGetImageData": "Cannot get image data", "@cannotGetImageData": {"description": "Error message when cannot retrieve image data"}, "saveOriginalImageFailed": "Failed to save original image: {error}", "@saveOriginalImageFailed": {"description": "Error message when saving original image fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "saveTranslatedImageNeedsContext": "Saving translated image requires Context", "@saveTranslatedImageNeedsContext": {"description": "Error message when context is missing for translated image save"}, "saveTranslatedImageUseRepaintBoundary": "Saving translated images requires RepaintBoundary implementation, please use saveCurrentDisplayImage method", "@saveTranslatedImageUseRepaintBoundary": {"description": "Error message suggesting to use RepaintBoundary for translated image save"}, "saveTranslatedImageFailed": "Failed to save translated image: {error}", "@saveTranslatedImageFailed": {"description": "Error message when saving translated image fails", "placeholders": {"error": {"type": "String", "example": "Rendering error"}}}, "savedSuccessfully": "{count} images saved successfully", "@savedSuccessfully": {"description": "Success message for batch save", "placeholders": {"count": {"type": "int", "example": "3"}}}, "savedPartially": "{successCount} images saved successfully, {failureCount} failed", "@savedPartially": {"description": "Partial success message for batch save", "placeholders": {"successCount": {"type": "int", "example": "2"}, "failureCount": {"type": "int", "example": "1"}}}, "errorDownloading": "Error downloading: {error}", "@errorDownloading": {"description": "Error message when download fails, from list.dart", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "loadingFailed": "Loading failed {error}", "@loadingFailed": {"description": "Error message when load failed", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "deleteConfirmation": "Delete {count} item{s}?", "@deleteConfirmation": {"description": "Delete confirmation message, from list.dart", "placeholders": {"count": {"type": "int", "example": "3"}, "s": {"type": "String", "example": "s"}}}, "drafts": "Pending Tasks", "@drafts": {"description": "Drafts page title"}, "toLanguage": "To: {lang}", "@toLanguage": {"description": "Target language selection text", "placeholders": {"lang": {"type": "String", "example": "English"}}}, "translateAll": "Translate All", "@translateAll": {"description": "Translate all button text"}, "nothingSelected": "Nothing selected", "@nothingSelected": {"description": "Message when no items are selected"}, "operationFailed": "Failed: {error}", "@operationFailed": {"description": "Operation failure message", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "allJobsDone": "All Jobs Done", "@allJobsDone": {"description": "Message when no drafts are available"}, "imageNotFound": "Image not found", "@imageNotFound": {"description": "Message shown when image fails to load"}, "usesLeftToday": "{count} Uses left today", "@usesLeftToday": {"description": "Remaining uses count message", "placeholders": {"count": {"type": "int", "example": "5"}}}, "upgradeToPro": "Upgrade to PRO for unlimited access", "@upgradeToPro": {"description": "Upgrade to pro message"}, "upgradeNow": "GET PRO", "@upgradeNow": {"description": "Upgrade button text"}, "updatingTranslationPosition": "Updating translation position...", "@updatingTranslationPosition": {"description": "Message shown when updating translation position during scrolling"}, "noImagesFound": "No images found or lazy loading in progress", "@noImagesFound": {"description": "Message shown when no images are found on the page"}, "localTranslationInitializing": "Initializing local translation services...", "@localTranslationInitializing": {"description": "Message shown when local translation services are initializing"}, "localTranslationFailed": "Local translation failed", "@localTranslationFailed": {"description": "Error message when local translation fails"}, "ocrServiceNotInitialized": "OCR service not initialized", "@ocrServiceNotInitialized": {"description": "Error message when OCR service is not initialized"}, "translationServiceNotInitialized": "Translation service not initialized", "@translationServiceNotInitialized": {"description": "Error message when translation service is not initialized"}, "processingImagesForTranslation": "Processing images for translation...", "@processingImagesForTranslation": {"description": "Message shown when processing images for local translation"}, "localTranslationCompleted": "Local translation completed for {count} images", "@localTranslationCompleted": {"description": "Message shown when local translation is completed", "placeholders": {"count": {"type": "int", "example": "3"}}}, "newTab": "New Tab", "@newTab": {"description": "Menu item to create a new tab"}, "addBookmark": "Add Bookmark", "@addBookmark": {"description": "Menu item to add current page to bookmarks"}, "removeBookmark": "Remove Bookmark", "@removeBookmark": {"description": "Menu item to remove current page from bookmarks"}, "unableToProcessImages": "Unable to find any images to process on the page.", "@unableToProcessImages": {"description": "Message shown when unable to process images on the page"}, "downloadFailed": "Download failed: {error}", "@downloadFailed": {"description": "Error message when download fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "selectAtLeastOneImage": "Please select at least one image", "@selectAtLeastOneImage": {"description": "Message shown when no images are selected"}, "noValidImagesToDownload": "No valid images to download", "@noValidImagesToDownload": {"description": "Message shown when there are no valid images to download"}, "failedToProcessImages": "Failed to process images: {error}", "@failedToProcessImages": {"description": "Error message when processing images fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "loadingImages": "Loading images...", "@loadingImages": {"description": "Message shown when loading images"}, "deleteThisItem": "Delete this item?", "@deleteThisItem": {"description": "Confirmation message when deleting an image in the viewer"}, "errorLoadingImageViewer": "Error loading image viewer", "@errorLoadingImageViewer": {"description": "Error message when image viewer fails to load"}, "failedToDeleteImage": "Failed to delete image", "@failedToDeleteImage": {"description": "Error message when image deletion fails"}, "completedTranslationAt": "Complete translation at \n{time}", "@completedTranslationAt": {"description": "Shows when a translation was completed", "placeholders": {"time": {"type": "String", "example": "2023-01-01 12:00"}}}, "dailyLimitReached": "Daily Limit Reached", "@dailyLimitReached": {"description": "Title shown when user reaches daily usage limit"}, "quotaResetMessage": "Free quota will reset tomorrow, Watch ads for more", "@quotaResetMessage": {"description": "Message explaining quota reset and upgrade option"}, "upgradeButton": "Upgrade", "@upgradeButton": {"description": "Button text for upgrading to PRO version"}, "tryTomorrowButton": "Try Tomorrow", "@tryTomorrowButton": {"description": "Button text for trying again tomorrow"}, "followSystem": "Follow System", "@followSystem": {"description": "Follow system language setting"}, "proTitle": "PRO", "@proTitle": {"description": "PRO title in user drawer"}, "unlockFileUpload": "Unlock file translation", "@unlockFileUpload": {"description": "Feature item: Unlock file upload"}, "highQualityTranslation": "High quality translation", "@highQualityTranslation": {"description": "Feature item: High quality translation"}, "adFreeExperience": "Ad-free experience", "@adFreeExperience": {"description": "Feature item: Ad-free experience"}, "getPro": "GET PRO", "@getPro": {"description": "Button text: Get PRO"}, "loadFailed": "Load Failed, please check your network connection", "@loadFailed": {"description": "Message shown when loading products fails"}, "back": "Back", "@back": {"description": "Back button text"}, "purchaseSuccessful": "Purchase successful", "@purchaseSuccessful": {"description": "Message shown when purchase is successful"}, "purchaseRestored": "Purchase restored", "@purchaseRestored": {"description": "Message shown when purchase is restored"}, "restoreFailed": "<PERSON><PERSON> failed: {error}", "@restoreFailed": {"description": "Message shown when restore fails", "placeholders": {"error": {"type": "String", "example": "network error"}}}, "weekly": "Weekly", "@weekly": {"description": "Weekly subscription option name"}, "annual": "Annual", "@annual": {"description": "Annual subscription option name"}, "free": "FREE", "@free": {"description": "Free subscription option name"}, "freeText": "3 Days Free Trial", "@freeText": {"description": "Free subscription option text"}, "billedMonthly": "Billed Monthly", "@billedMonthly": {"description": "Monthly text"}, "billedAnnual": "Billed Annual", "@billedAnnual": {"description": "Annual text"}, "billedWeekly": "Billed Weekly", "@billedWeekly": {"description": "weekly text"}, "freeTrialText": "3 Days Free Trial", "@freeTrialText": {"description": "Free trial text"}, "save30Percent": "Save 30%", "@save30Percent": {"description": "Save 30% text"}, "loginTitle": "Login to your\n Account", "@loginTitle": {"description": "Login title"}, "watchAd": "Get More", "@watchAd": {"description": "Watch ad button text"}, "watchAdDescription": "Watch an ad to get more usage quota", "@watchAdDescription": {"description": "Watch ad description text"}, "fontSettings": "Font Settings", "@fontSettings": {"description": "Font settings section title"}, "fontFamily": "Font Family", "@fontFamily": {"description": "Font family setting label"}, "strokeSettings": "Stroke Settings", "@strokeSettings": {"description": "Stroke settings section title"}, "textStyleSettings": "Text Style Settings", "@textStyleSettings": {"description": "Text overlay settings panel title"}, "resetToDefaults": "Reset to Defaults", "@resetToDefaults": {"description": "Reset settings button tooltip"}, "fontSize": "Font Size", "@fontSize": {"description": "Font size setting label"}, "fontWeight": "Font Weight", "@fontWeight": {"description": "Font weight setting label"}, "textColor": "Text Color", "@textColor": {"description": "Text color setting section title"}, "textLabel": "Text", "@textLabel": {"description": "Text color picker label"}, "strokeLabel": "Stroke", "@strokeLabel": {"description": "Stroke color picker label"}, "strokeWidth": "Stroke Width", "@strokeWidth": {"description": "Stroke width setting label"}, "shadowEffect": "Shadow Effect", "@shadowEffect": {"description": "Shadow effect setting section title"}, "opacity": "Opacity", "@opacity": {"description": "Shadow opacity setting label"}, "horizontalOffset": "Horizontal Offset", "@horizontalOffset": {"description": "Shadow horizontal offset setting label"}, "verticalOffset": "Vertical Offset", "@verticalOffset": {"description": "Shadow vertical offset setting label"}, "blurRadius": "Blur <PERSON>dius", "@blurRadius": {"description": "Shadow blur radius setting label"}, "fontWeightThin": "Thin", "@fontWeightThin": {"description": "Font weight option: thin"}, "fontWeightNormal": "Normal", "@fontWeightNormal": {"description": "Font weight option: normal"}, "fontWeightMedium": "Medium", "@fontWeightMedium": {"description": "Font weight option: medium"}, "fontWeightSemiBold": "Semi Bold", "@fontWeightSemiBold": {"description": "Font weight option: semi bold"}, "fontWeightBold": "Bold", "@fontWeightBold": {"description": "Font weight option: bold"}, "fontWeightExtraBold": "Extra Bold", "@fontWeightExtraBold": {"description": "Font weight option: extra bold"}, "fontWeightBlack": "Black", "@fontWeightBlack": {"description": "Font weight option: black"}, "recommendedWebsites": "Recommended Websites", "@recommendedWebsites": {"description": "Header for recommended websites section"}, "myBookmarks": "Bookmarks", "@myBookmarks": {"description": "Header for bookmarks section"}, "noBookmarks": "No bookmarks yet", "@noBookmarks": {"description": "Empty state message when no bookmarks exist"}, "bookmarkAdded": "Bookmark added", "@bookmarkAdded": {"description": "Success message when bookmark is added"}, "alreadyBookmarked": "Already bookmarked", "@alreadyBookmarked": {"description": "Message when trying to bookmark an already bookmarked page"}, "cannotBookmarkEmptyPage": "Cannot bookmark empty page", "@cannotBookmarkEmptyPage": {"description": "Error message when trying to bookmark an empty page"}, "bookmarkFailed": "Failed to add bookmark", "@bookmarkFailed": {"description": "Error message when bookmark operation fails"}, "deleteBookmark": "Delete Bookmark", "@deleteBookmark": {"description": "Title for delete bookmark dialog"}, "deleteBookmarkConfirm": "Are you sure you want to delete bookmark \"{title}\"?", "@deleteBookmarkConfirm": {"description": "Confirmation message for deleting a bookmark", "placeholders": {"title": {"type": "String", "description": "The title of the bookmark to be deleted"}}}, "bookmarkDeleted": "Bookmark deleted", "@bookmarkDeleted": {"description": "Success message when bookmark is deleted"}, "manageBookmarks": "Manage", "@manageBookmarks": {"description": "Button text to manage all bookmarks"}, "allBookmarks": "All Bookmarks", "@allBookmarks": {"description": "Title for the full bookmarks management page"}, "browsingHistory": "History", "@browsingHistory": {"description": "Header for browsing history section"}, "noBrowsingHistory": "No browsing history yet", "@noBrowsingHistory": {"description": "Empty state message when no browsing history exists"}, "deleteHistory": "Delete History", "@deleteHistory": {"description": "Title for delete history dialog"}, "deleteHistoryConfirm": "Are you sure you want to delete this history item?", "@deleteHistoryConfirm": {"description": "Confirmation message for deleting a history item"}, "historyDeleted": "History item deleted", "@historyDeleted": {"description": "Success message when history item is deleted"}, "justNow": "Just now", "@justNow": {"description": "Timestamp for very recent history items"}, "minutesAgo": "{count} minutes ago", "@minutesAgo": {"description": "Timestamp for history items from minutes ago", "placeholders": {"count": {"type": "int", "description": "Number of minutes"}}}, "hoursAgo": "{count} hours ago", "@hoursAgo": {"description": "Timestamp for history items from hours ago", "placeholders": {"count": {"type": "int", "description": "Number of hours"}}}, "daysAgo": "{count} days ago", "@daysAgo": {"description": "Timestamp for history items from days ago", "placeholders": {"count": {"type": "int", "description": "Number of days"}}}, "downloadingLanguageFiles": "Downloading language files ({languageName})", "@downloadingLanguageFiles": {"description": "Message shown when downloading language model files", "placeholders": {"languageName": {"type": "String", "description": "Name of the language being downloaded"}}}, "translationInProgress": "Translation in progress...", "@translationInProgress": {"description": "Message shown when translation is being processed"}, "translateThisImage": "Translate this image", "@translateThisImage": {"description": "Tooltip for the floating translate button on images"}, "imageTranslationLoading": "Translating image...", "@imageTranslationLoading": {"description": "Loading message shown when translating a specific image"}, "downloadTimeout": "Download timeout, please check your network connection", "@downloadTimeout": {"description": "Error message when download times out"}}