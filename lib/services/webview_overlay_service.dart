import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:imtrans/services/local_ocr_service.dart';
import 'package:imtrans/services/webview_js_resources.dart';
import 'package:imtrans/services/webview_injection_service.dart';

/// Result of font size calculation with potential region adjustments
class FontSizeCalculationResult {
  final double fontSize;
  final double adjustedWidth;
  final double adjustedHeight;
  final bool wasExpanded;
  final String? expansionReason;

  const FontSizeCalculationResult({
    required this.fontSize,
    required this.adjustedWidth,
    required this.adjustedHeight,
    required this.wasExpanded,
    this.expansionReason,
  });

  @override
  String toString() {
    return 'FontSizeCalculationResult(fontSize: $fontSize, '
           'adjustedSize: ${adjustedWidth}x$adjustedHeight, '
           'wasExpanded: $wasExpanded'
           '${expansionReason != null ? ', reason: $expansionReason' : ''})';
  }
}

/// Service for managing JavaScript-based text overlays in WebView
/// Handles injection, positioning, and cleanup of translated text overlays
class WebViewOverlayService {
  static final WebViewOverlayService _instance = WebViewOverlayService._internal();
  factory WebViewOverlayService() => _instance;
  WebViewOverlayService._internal();

  // Core state
  bool _isInitialized = false;
  bool _overlaysActive = false;
  InAppWebViewController? _currentController;
  final List<String> _activeOverlayIds = [];

  // Services
  final WebViewInjectionService _injectionService = WebViewInjectionService();

  // Constants
  static const String _overlayNamespace = 'translationOverlays';
  static const String _overlayStyleId = 'translation-overlay-styles';

  // =============================================================================
  // INITIALIZATION AND CORE METHODS
  // =============================================================================

  /// Initialize the overlay service
  Future<void> initialize() async {
    try {
      _isInitialized = true;
      debugPrint('WebViewOverlayService: Initialized successfully');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to initialize - $e');
      throw OverlayException('Failed to initialize overlay service: $e');
    }
  }

  /// Helper method for safe JavaScript execution with proper type casting
  Future<Map<String, dynamic>?> _executeJavaScriptSafely(String script, {String? operationName}) async {
    if (_currentController == null) {
      debugPrint('WebViewOverlayService: No controller available for ${operationName ?? 'JavaScript execution'}');
      return null;
    }

    try {
      final result = await _currentController!.evaluateJavascript(source: script);

      // Handle different return types from JavaScript
      if (result == null) {
        return null;
      } else if (result is Map<Object?, Object?>) {
        // Convert Map<Object?, Object?> to Map<String, dynamic>
        return result.map((key, value) => MapEntry(key.toString(), value));
      } else if (result is Map<String, dynamic>) {
        return result;
      } else {
        debugPrint('WebViewOverlayService: Unexpected result type for ${operationName ?? 'operation'}: ${result.runtimeType}');
        return {'result': result};
      }
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to execute JavaScript for ${operationName ?? 'operation'} - $e');
      return null;
    }
  }

  /// Helper method for simple JavaScript execution without return value
  Future<bool> _executeJavaScriptSimple(String script, {String? operationName}) async {
    if (_currentController == null) {
      debugPrint('WebViewOverlayService: No controller available for ${operationName ?? 'JavaScript execution'}');
      return false;
    }

    try {
      await _currentController!.evaluateJavascript(source: script);
      return true;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to execute JavaScript for ${operationName ?? 'operation'} - $e');
      return false;
    }
  }

  // =============================================================================
  // GETTERS AND SETTERS
  // =============================================================================

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if overlays are currently active
  bool get overlaysActive => _overlaysActive;

  /// Set the current WebView controller
  void setController(InAppWebViewController? controller) {
    _currentController = controller;
  }

  /// Check WebView readiness for translation operations
  Future<bool> checkWebViewReadiness() async {
    return await _injectionService.isWebViewReady(_currentController!);
  }

  /// Inject CSS styles for overlays using the injection service
  Future<bool> _injectOverlayStyles() async {
    if (_currentController == null) {
      debugPrint('WebViewOverlayService: No controller available for style injection');
      return false;
    }

    try {
      // Load CSS content using the resource management system
      final cssContent = await WebViewJsResources.getContent('translation_overlay_styles');

      if (cssContent.isEmpty) {
        debugPrint('WebViewOverlayService: CSS content is empty');
        return false;
      }

      // Use injection service for consistent CSS injection
      final success = await _injectionService.injectCss(
        _currentController!,
        cssContent,
        styleId: _overlayStyleId,
      );

      debugPrint('WebViewOverlayService: Style injection result: $success');
      return success;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject styles - $e');
      return false;
    }
  }

  /// Inject JavaScript functions for overlay management
  Future<void> _injectOverlayScripts() async {
    if (_currentController == null) {
      debugPrint('WebViewOverlayService: No controller available for script injection');
      return;
    }

    try {
      // 使用新的资源管理系统加载 JavaScript
      final jsContent = await WebViewJsResources.getContent('translation_overlay');
      debugPrint('WebViewOverlayService: Loaded JS content length: ${jsContent.length}');
      
      // 使用注入服务注入 JavaScript
      final injectionService = WebViewInjectionService();
      
      // 清除之前的注入记录，强制重新注入
      injectionService.removeInjectionRecord('js_translationOverlays');
      
      final success = await injectionService.injectJavaScript(
        _currentController!,
        jsContent,
        namespace: 'translationOverlays',
      );
      
      debugPrint('WebViewOverlayService: Script injection result: $success');
      
      // 验证注入是否成功
      if (success) {
        final checkResult = await _currentController!.evaluateJavascript(source: '''
          (function() {
            return {
              translationOverlaysExists: !!window.translationOverlays,
              hasInitialize: !!(window.translationOverlays && window.translationOverlays.initialize),
              hasInitIntersectionObserver: !!(window.translationOverlays && window.translationOverlays.initIntersectionObserver)
            };
          })();
        ''');
        debugPrint('WebViewOverlayService: Injection verification: $checkResult');
      }
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject scripts - $e');
    }
  }

  /// Force re-inject all overlay resources (for debugging)
  Future<void> forceReinject() async {
    if (_currentController == null) {
      debugPrint('WebViewOverlayService: No controller available for force re-injection');
      return;
    }

    try {
      debugPrint('WebViewOverlayService: Force re-injecting overlay resources...');
      
      // 清除所有现有的覆盖层
      await _currentController!.evaluateJavascript(source: '''
        // 移除所有现有的覆盖层元素
        const existingOverlays = document.querySelectorAll('.translation-overlay');
        existingOverlays.forEach(overlay => overlay.remove());
        
        // 移除所有加载指示器
        const existingIndicators = document.querySelectorAll('.translation-loading-overlay');
        existingIndicators.forEach(indicator => indicator.remove());
        
        // 移除现有的样式
        const existingStyles = document.getElementById('translation-overlay-styles');
        if (existingStyles) {
          existingStyles.remove();
        }
        
        // 清除 window.translationOverlays
        if (window.translationOverlays) {
          delete window.translationOverlays;
        }
      ''');
      
      // 重新注入样式和脚本
      await _injectOverlayStyles();
      await _injectOverlayScripts();
      
      // 验证注入
      final verification = await _currentController!.evaluateJavascript(source: '''
        (function() {
          const stylesExist = !!document.getElementById('translation-overlay-styles');
          const translationOverlaysExists = !!window.translationOverlays;
          const hasInitialize = !!(window.translationOverlays && window.translationOverlays.initialize);
          const hasShowLoadingIndicator = !!(window.translationOverlays && window.translationOverlays.showLoadingIndicator);
          const hasCreateImageOverlay = !!(window.translationOverlays && window.translationOverlays.createImageOverlay);
          
          return {
            stylesExist: stylesExist,
            translationOverlaysExists: translationOverlaysExists,
            hasInitialize: hasInitialize,
            hasShowLoadingIndicator: hasShowLoadingIndicator,
            hasCreateImageOverlay: hasCreateImageOverlay
          };
        })();
      ''');
      
      debugPrint('WebViewOverlayService: Force re-injection verification: $verification');
      
      if (verification != null && verification['translationOverlaysExists'] == true) {
        // 重新初始化
        final initResult = await _currentController!.evaluateJavascript(
          source: 'window.translationOverlays.initialize();'
        );
        debugPrint('WebViewOverlayService: Re-initialization result: $initResult');
      }
      
    } catch (e) {
      debugPrint('WebViewOverlayService: Force re-injection failed - $e');
    }
  }

  /// High-level method to show translation overlays
  Future<void> showTranslationOverlays({
    required List<OcrTextElement> translatedElements,
    required List<String> imageUrls,
  }) async {
    return showOverlays(translatedElements, imageUrls);
  }

  /// Initialize the overlay system with intersection observer
  Future<void> initializeOverlaySystem() async {
    if (_currentController == null) {
      debugPrint('WebViewOverlayService: No WebView controller available for overlay system initialization');
      return;
    }

    try {
      // 首先检查 WebView 是否准备好
      final readyCheck = await _currentController!.evaluateJavascript(source: '''
        (function() {
          return {
            documentReady: !!document,
            bodyExists: !!document.body,
            headExists: !!document.head
          };
        })();
      ''');
      
      debugPrint('WebViewOverlayService: WebView readiness check: $readyCheck');
      
      if (readyCheck == null) {
        debugPrint('WebViewOverlayService: WebView not ready for JavaScript execution');
        return;
      }

      // 检查是否已经注入
      final existingCheck = await _currentController!.evaluateJavascript(source: '''
        (function() {
          return {
            stylesExist: !!document.getElementById('translation-overlay-styles'),
            translationOverlaysExists: !!window.translationOverlays,
            hasInitialize: !!(window.translationOverlays && window.translationOverlays.initialize)
          };
        })();
      ''');
      
      debugPrint('WebViewOverlayService: Existing resources check: $existingCheck');
      
      // 只有在资源不存在时才注入
      if (existingCheck == null || existingCheck['translationOverlaysExists'] != true) {
        await _injectOverlayStyles();
        await _injectOverlayScripts();
      }

      // 检查注入是否成功
      final checkResult = await _currentController!.evaluateJavascript(source: '''
        (function() {
          return {
            translationOverlaysExists: !!window.translationOverlays,
            hasInitialize: !!(window.translationOverlays && window.translationOverlays.initialize),
            hasInitIntersectionObserver: !!(window.translationOverlays && window.translationOverlays.initIntersectionObserver)
          };
        })();
      ''');
      debugPrint('WebViewOverlayService: Pre-initialization check: $checkResult');

      if (checkResult != null && checkResult['translationOverlaysExists'] == true) {
        final initResult = await _currentController!.evaluateJavascript(
          source: 'window.translationOverlays.initialize();'
        );
        
        debugPrint('WebViewOverlayService: Overlay system initialization result: $initResult');
      } else {
        debugPrint('WebViewOverlayService: Translation overlays not available for initialization');
      }

      debugPrint('WebViewOverlayService: Overlay system initialized');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to initialize overlay system - $e');
    }
  }

  // =============================================================================
  // LOADING INDICATOR METHODS
  // =============================================================================

  /// Show loading indicator for a specific image
  Future<void> showLoadingIndicator(String imageUrl, {String? loadingText}) async {
    final text = loadingText ?? 'Translating...';
    final script = "window.translationOverlays.showLoadingIndicator('$imageUrl', '$text');";

    final success = await _executeJavaScriptSimple(script, operationName: 'show loading indicator');
    if (success) {
      debugPrint('WebViewOverlayService: Loading indicator shown for $imageUrl');
    }
  }

  /// Hide loading indicator for a specific image
  Future<void> hideLoadingIndicator(String imageUrl) async {
    final script = "window.translationOverlays.hideLoadingIndicator('$imageUrl');";

    final success = await _executeJavaScriptSimple(script, operationName: 'hide loading indicator');
    if (success) {
      debugPrint('WebViewOverlayService: Loading indicator hidden for $imageUrl');
    }
  }

  /// Force hide all loading indicators (cleanup function)
  Future<void> hideAllLoadingIndicators() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.hideAllLoadingIndicators();
      ''');
      // debugPrint('WebViewOverlayService: All loading indicators hidden');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide all loading indicators - $e');
    }
  }

  /// Debug loading indicators status
  Future<void> debugLoadingIndicators() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.debugLoadingIndicators();
      ''');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to debug loading indicators - $e');
    }
  }

  /// Show cache indicator for a cached image
  Future<void> showCacheIndicator(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.showCacheIndicator('$imageUrl');
      ''');
      // debugPrint('WebViewOverlayService: Cache indicator shown for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to show cache indicator - $e');
    }
  }

  /// Hide cache indicator for a specific image
  Future<void> hideCacheIndicator(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.hideCacheIndicator('$imageUrl');
      ''');
      // debugPrint('WebViewOverlayService: Cache indicator hidden for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide cache indicator - $e');
    }
  }

  /// Hide all cache indicators
  Future<void> hideAllCacheIndicators() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.hideAllCacheIndicators();
      ''');
      // debugPrint('WebViewOverlayService: All cache indicators hidden');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide all cache indicators - $e');
    }
  }

  // =============================================================================
  // 图片可见性监控和清理方法（改进版：实时检测 + 智能清理）
  // =============================================================================

  /// 启动图片可见性监控（改进版：实时检测 + 定期备用检查）
  Future<void> startVisibilityMonitoring() async {
    const script = "window.translationOverlays.initVisibilityMonitoring();";

    final success = await _executeJavaScriptSimple(script, operationName: 'start visibility monitoring');
    if (success) {
      debugPrint('WebViewOverlayService: 图片可见性监控已启动（实时检测 + 定期备用检查）');
    }
  }

  /// 启动增强的图片切换检测（专门针对MangaDX等网站）
  Future<void> startEnhancedImageSwitchDetection() async {
    const script = '''
      (function() {
        // 专门针对漫画网站的图片切换检测
        if (window.translationOverlays) {
          window.translationOverlays.initVisibilityMonitoring();

          // 额外监控常见的图片容器变化
          const imageContainers = document.querySelectorAll('[class*="image"], [class*="page"], [id*="image"], [id*="page"]');
          console.log('找到', imageContainers.length, '个可能的图片容器，开始监控');

          return true;
        }
        return false;
      })();
    ''';

    final success = await _executeJavaScriptSimple(script, operationName: 'start enhanced image switch detection');
    if (success) {
      debugPrint('WebViewOverlayService: 增强图片切换检测已启动，专门优化漫画网站体验');
    }
  }

  /// 停止图片可见性监控
  Future<void> stopVisibilityMonitoring() async {
    const script = "window.translationOverlays.stopVisibilityMonitoring();";

    final success = await _executeJavaScriptSimple(script, operationName: 'stop visibility monitoring');
    if (success) {
      debugPrint('WebViewOverlayService: 图片可见性监控已停止');
    }
  }

  /// 检查指定图片是否仍然可见
  Future<bool> isImageStillVisible(String imageUrl) async {
    final script = '''
      (function() {
        return window.translationOverlays.isImageStillVisible('$imageUrl');
      })();
    ''';

    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: script);
      return result == true;
    } catch (e) {
      debugPrint('WebViewOverlayService: 检查图片可见性失败 - $e');
      return false;
    }
  }

  /// 完全清理指定图片的所有相关内容
  Future<void> cleanupImageCompletely(String imageUrl) async {
    final script = '''
      (function() {
        return window.translationOverlays.cleanupImageCompletely('$imageUrl');
      })();
    ''';

    final success = await _executeJavaScriptSimple(script, operationName: 'cleanup image completely');
    if (success) {
      debugPrint('WebViewOverlayService: 已完全清理图片相关内容: $imageUrl');

      // 从活动overlay列表中移除相关ID
      _activeOverlayIds.removeWhere((id) => id.contains(imageUrl.hashCode.toString()));
    }
  }

  /// 移除指定图片的所有overlay（改进版）
  Future<void> removeImageOverlays(String imageUrl) async {
    final script = "window.translationOverlays.removeImageOverlays('$imageUrl');";

    final success = await _executeJavaScriptSimple(script, operationName: 'remove image overlays');
    if (success) {
      debugPrint('WebViewOverlayService: 已移除图片overlay: $imageUrl');

      // 从活动overlay列表中移除相关ID
      _activeOverlayIds.removeWhere((id) => id.contains(imageUrl.hashCode.toString()));
    }
  }

  /// 移除指定图片的操作按钮（新版本）
  Future<void> removeImageActionButton(String imageUrl) async {
    final script = "window.translationOverlays.removeActionButton('$imageUrl');";

    final success = await _executeJavaScriptSimple(script, operationName: 'remove action button');
    if (success) {
      debugPrint('WebViewOverlayService: 已移除图片操作按钮: $imageUrl');
    }
  }

  /// 处理图片隐藏事件的回调方法（改进版）
  /// 当JavaScript实时检测到图片被隐藏时会调用此方法
  Future<void> handleImageHidden(String imageUrl) async {
    debugPrint('WebViewOverlayService: 收到图片隐藏事件（实时检测）: $imageUrl');

    try {
      // 执行完全清理
      await cleanupImageCompletely(imageUrl);

      // 从活动overlay列表中移除相关ID
      _activeOverlayIds.removeWhere((id) => id.contains(imageUrl.hashCode.toString()));

      debugPrint('WebViewOverlayService: 已处理图片隐藏事件，完成实时清理: $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: 处理图片隐藏事件时发生错误: $imageUrl - $e');
    }
  }

  /// 处理图片可见事件的回调方法（新增）
  /// 当JavaScript检测到图片重新可见时会调用此方法
  Future<void> handleImageVisible(String imageUrl) async {
    debugPrint('WebViewOverlayService: 收到图片可见事件（实时检测）: $imageUrl');

    try {
      // 检查图片是否确实可见
      final isVisible = await isImageStillVisible(imageUrl);
      if (isVisible) {
        debugPrint('WebViewOverlayService: 确认图片重新可见，可以使用翻译功能: $imageUrl');

        // 可以在这里触发重新初始化overlay观察的逻辑
        // 但通常IntersectionObserver会自动处理新可见的图片
      } else {
        debugPrint('WebViewOverlayService: 图片可见事件误报，图片实际不可见: $imageUrl');
      }
    } catch (e) {
      debugPrint('WebViewOverlayService: 处理图片可见事件时发生错误: $imageUrl - $e');
    }
  }

  /// 获取图片状态映射信息（调试用）
  Future<Map<String, dynamic>?> getImageStateMap() async {
    const script = '''
      (function() {
        if (window.translationOverlays && window.translationOverlays.imageStateMap) {
          const stateMap = {};
          window.translationOverlays.imageStateMap.forEach((state, imageUrl) => {
            stateMap[imageUrl] = {
              isVisible: state.isVisible,
              lastChecked: state.lastChecked,
              hasOverlay: state.hasOverlay,
              timeSinceLastCheck: Date.now() - state.lastChecked
            };
          });
          return {
            totalImages: window.translationOverlays.imageStateMap.size,
            stateMap: stateMap,
            monitoringActive: !!window.translationOverlays.mutationObserver
          };
        }
        return null;
      })();
    ''';

    if (_currentController == null) return null;

    try {
      final result = await _currentController!.evaluateJavascript(source: script);
      return result as Map<String, dynamic>?;
    } catch (e) {
      debugPrint('WebViewOverlayService: 获取图片状态映射失败 - $e');
      return null;
    }
  }

  /// 批量检查并清理隐藏的图片
  Future<void> checkAndCleanHiddenImages() async {
    const script = '''
      (function() {
        if (window.translationOverlays && window.translationOverlays.checkAndCleanHiddenImages) {
          window.translationOverlays.checkAndCleanHiddenImages();
          return true;
        }
        return false;
      })();
    ''';

    final success = await _executeJavaScriptSimple(script, operationName: 'check and clean hidden images');
    if (success) {
      debugPrint('WebViewOverlayService: 已执行隐藏图片检查和清理');
    }
  }

  /// 清理JavaScript端的处理状态（防重复处理）
  Future<void> clearProcessingStates() async {
    const script = '''
      (function() {
        if (window.translationOverlays) {
          // 清理处理中的图片集合
          window.translationOverlays.processingImages.clear();
          console.log('已清理JavaScript端的图片处理状态');
          return true;
        }
        return false;
      })();
    ''';

    final success = await _executeJavaScriptSimple(script, operationName: 'clear processing states');
    if (success) {
      debugPrint('WebViewOverlayService: 已清理JavaScript端的处理状态');
    }
  }

  /// Check if image is already cached/processed
  Future<bool> isImageCached(String imageUrl) async {
    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.isImageCached('$imageUrl');
      ''');
      return result == true;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to check cache status - $e');
      return false;
    }
  }

  /// Check if image is currently being processed
  Future<bool> isImageProcessing(String imageUrl) async {
    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.isImageProcessing('$imageUrl');
      ''');
      return result == true;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to check processing status - $e');
      return false;
    }
  }

  /// Clear cache for specific image
  Future<void> clearImageCache(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.clearImageCache('$imageUrl');
      ''');
      // debugPrint('WebViewOverlayService: Cache cleared for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to clear image cache - $e');
    }
  }

  /// Clear all translation cache
  Future<void> clearAllCache() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.clearAllCache();
      ''');
      // debugPrint('WebViewOverlayService: All cache cleared');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to clear all cache - $e');
    }
  }

  /// Enable or disable translation mode (shows/hides action buttons)
  Future<void> setTranslationMode(bool enabled, {String? buttonText}) async {
    if (_currentController == null) return;

    final text = buttonText ?? 'Translate this image';

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.setTranslationMode($enabled, '$text');
      ''');
      // debugPrint('WebViewOverlayService: Translation mode ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to set translation mode - $e');
    }
  }

  /// Update action button state for a specific image
  Future<void> updateActionButtonState(String imageUrl, String state) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.updateActionButtonState('$imageUrl', '$state');
      ''');
      // debugPrint('WebViewOverlayService: Updated action button state for $imageUrl to $state');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to update action button state - $e');
    }
  }

  /// Remove action button for a specific image
  Future<void> removeActionButton(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.removeActionButton('$imageUrl');
      ''');
      // debugPrint('WebViewOverlayService: Removed action button for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to remove action button - $e');
    }
  }

  /// Create and display overlays for translated text elements
  Future<void> showOverlays(List<OcrTextElement> translatedElements, List<String> imageUrls) async {
    if (!_isInitialized || _currentController == null) {
      throw OverlayException('Overlay service not initialized or controller not set');
    }

    try {
      // 确保覆盖层系统正确初始化
      debugPrint('WebViewOverlayService: Starting overlay display process');

      // 首先清理所有现有的翻译浮层，防止上一页内容残留
      debugPrint('WebViewOverlayService: Cleaning up existing overlays before showing new ones');
      await _cleanupAllOverlays();

      // Check if translation overlays system is available (don't force reinject)
      final systemCheck = await _currentController!.evaluateJavascript(source: '''
        (function() {
          return {
            translationOverlaysExists: !!window.translationOverlays,
            hasCreateImageOverlay: !!(window.translationOverlays && window.translationOverlays.createImageOverlay),
            hasHideLoadingIndicator: !!(window.translationOverlays && window.translationOverlays.hideLoadingIndicator)
          };
        })();
      ''');

      debugPrint('WebViewOverlayService: System check result: $systemCheck');

      // Only reinject if system is not available
      if (systemCheck == null || systemCheck['translationOverlaysExists'] != true) {
        debugPrint('WebViewOverlayService: Translation overlays system not available, reinitializing...');
        await forceReinject();
      }

      debugPrint('WebViewOverlayService: Creating overlays for ${translatedElements.length} elements and ${imageUrls.length} images');

      // Group elements by image (assuming elements are ordered by image)
      int elementsPerImage = translatedElements.length ~/ imageUrls.length;
      if (elementsPerImage == 0) elementsPerImage = 1;

      debugPrint('WebViewOverlayService: Elements per image: $elementsPerImage');

      // Create overlays for each translated element
      for (int i = 0; i < translatedElements.length; i++) {
        final element = translatedElements[i];
        final overlayId = 'overlay_${DateTime.now().millisecondsSinceEpoch}_$i'; // Unique ID

        // 简化元素分组逻辑：假设所有元素都属于第一个图片
        final imageUrl = imageUrls.isNotEmpty ? imageUrls.first : '';

        final boundingBox = element.boundingBox;

        // Use optimized font size calculation with text fitting
        final fontSizeResult = _calculateOptimalFontSize(
          translatedText: element.text,
          originalWidth: boundingBox.width,
          originalHeight: boundingBox.height,
        );

        debugPrint('WebViewOverlayService: Creating overlay $overlayId for text "${element.text}" at (${boundingBox.left}, ${boundingBox.top}) on image $imageUrl');
        debugPrint('WebViewOverlayService: Original bounding box: ${boundingBox.width}x${boundingBox.height}');
        debugPrint('WebViewOverlayService: Font size calculation result: $fontSizeResult');

        // Create overlay positioned relative to the specific image
        try {
          // 先验证 translationOverlays 是否可用
          final verifyResult = await _currentController!.evaluateJavascript(source: '''
            (function() {
              return {
                translationOverlaysExists: !!window.translationOverlays,
                hasCreateImageOverlay: !!(window.translationOverlays && window.translationOverlays.createImageOverlay)
              };
            })();
          ''');

          // debugPrint('WebViewOverlayService: Verification before creating $overlayId: $verifyResult');

          if (verifyResult != null && verifyResult['hasCreateImageOverlay'] == true) {
            // Create overlay with proper error handling
            final result = await _currentController!.evaluateJavascript(source: '''
              (function() {
                try {
                  const overlay = window.translationOverlays.createImageOverlay(
                    '$overlayId',
                    ${_escapeJavaScriptString(element.text)},
                    '$imageUrl',
                    ${boundingBox.left},
                    ${boundingBox.top},
                    ${fontSizeResult.adjustedWidth},
                    ${fontSizeResult.adjustedHeight},
                    ${fontSizeResult.fontSize}
                  );
                  return {
                    success: true,
                    overlayCreated: !!overlay,
                    overlayId: overlay ? overlay.id : null
                  };
                } catch (error) {
                  console.error('Error creating overlay:', error);
                  return {
                    success: false,
                    error: error.toString()
                  };
                }
              })();
            ''');

            debugPrint('WebViewOverlayService: Overlay creation result for $overlayId: $result');

            if (result != null && result['success'] == true) {
              _activeOverlayIds.add(overlayId);
              // debugPrint('WebViewOverlayService: Successfully created overlay $overlayId');
            } else {
              debugPrint('WebViewOverlayService: Failed to create overlay $overlayId: ${result?['error']}');
              // 备用方案：直接创建覆盖层元素
              await _createSimpleOverlay(overlayId, element.text, boundingBox.left.toDouble(), boundingBox.top.toDouble(), fontSizeResult.fontSize,
                width: fontSizeResult.adjustedWidth, height: fontSizeResult.adjustedHeight);
            }
          } else {
            debugPrint('WebViewOverlayService: translationOverlays.createImageOverlay not available for $overlayId');

            // 备用方案：直接创建覆盖层元素
            await _createSimpleOverlay(overlayId, element.text, boundingBox.left.toDouble(), boundingBox.top.toDouble(), fontSizeResult.fontSize,
              width: fontSizeResult.adjustedWidth, height: fontSizeResult.adjustedHeight);
          }
        } catch (e) {
          debugPrint('WebViewOverlayService: Failed to create overlay $overlayId: $e');
          // 备用方案：直接创建覆盖层元素
          try {
            await _createSimpleOverlay(overlayId, element.text, boundingBox.left.toDouble(), boundingBox.top.toDouble(), fontSizeResult.fontSize,
              width: fontSizeResult.adjustedWidth, height: fontSizeResult.adjustedHeight);
          } catch (fallbackError) {
            debugPrint('WebViewOverlayService: Fallback overlay creation also failed for $overlayId: $fallbackError');
          }
        }
      }

      _overlaysActive = true;
      
      // 调试：检查覆盖层是否正确创建
      // final debugResult = await _currentController!.evaluateJavascript(source: '''
      //   (function() {
      //     const overlays = document.querySelectorAll('.translation-overlay');
      //     const overlayCount = overlays.length;
      //     const overlayInfo = Array.from(overlays).map((overlay, index) => ({
      //       id: overlay.id,
      //       text: overlay.textContent,
      //       visible: overlay.offsetParent !== null,
      //       opacity: window.getComputedStyle(overlay).opacity,
      //       zIndex: window.getComputedStyle(overlay).zIndex,
      //       position: {
      //         left: overlay.style.left,
      //         top: overlay.style.top,
      //         width: overlay.style.width,
      //         height: overlay.style.height
      //       }
      //     }));
      //     return {
      //       overlayCount: overlayCount,
      //       overlays: overlayInfo,
      //       translationOverlaysExists: !!window.translationOverlays,
      //       overlaysMapSize: window.translationOverlays ? window.translationOverlays.overlays.size : 0
      //     };
      //   })();
      // ''');
      // debugPrint('WebViewOverlayService: Overlay debug info: $debugResult');
      
      // 添加位置调试
      // await debugOverlayPositions();
      
      // 添加按钮调试
      // await debugActionButtons();
      
      // 添加图片和覆盖层位置调试
      // await debugImageAndOverlayPositioning();
      
      // 添加覆盖层渲染调试
      await debugOverlayRendering();
      
      // debugPrint('WebViewOverlayService: Created ${translatedElements.length} overlays for ${imageUrls.length} images');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to show overlays - $e');
      throw OverlayException('Failed to show overlays: $e');
    }
  }

  /// Hide all overlays
  Future<void> hideOverlays() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeAllOverlays();'
      );
      
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeScrollHandler();'
      );

      _activeOverlayIds.clear();
      _overlaysActive = false;
      // debugPrint('WebViewOverlayService: Hidden all overlays');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide overlays - $e');
    }
  }

  /// 彻底清理所有翻译浮层（防止上一页内容残留）
  Future<void> _cleanupAllOverlays() async {
    if (_currentController == null) return;

    try {
      // 执行彻底的浮层清理
      await _currentController!.evaluateJavascript(source: '''
        (function() {
          if (window.translationOverlays) {
            // 移除所有翻译浮层
            window.translationOverlays.removeAllOverlays();

            // 清理所有相关的数据结构
            window.translationOverlays.overlays.clear();
            window.translationOverlays.loadingOverlays.clear();
            window.translationOverlays.actionButtons.clear();
            window.translationOverlays.imageCache.clear();

            // 清理处理状态
            window.translationOverlays.processingImages.clear();

            console.log('WebViewOverlayService: 已彻底清理所有翻译浮层和相关状态');
            return true;
          }
          return false;
        })();
      ''');

      // 清理Dart端的状态
      _activeOverlayIds.clear();
      _overlaysActive = false;

      debugPrint('WebViewOverlayService: 已彻底清理所有翻译浮层，防止上一页内容残留');
    } catch (e) {
      debugPrint('WebViewOverlayService: 清理所有浮层时发生错误 - $e');
    }
  }

  /// Clean up all overlays and handlers
  Future<void> cleanup() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      // 停止图片可见性监控
      await stopVisibilityMonitoring();

      await hideOverlays();

      // Remove injected styles
      await _currentController!.evaluateJavascript(source: '''
        const styleElement = document.getElementById('translation-overlay-styles');
        if (styleElement) {
          styleElement.parentNode.removeChild(styleElement);
        }
      ''');

      debugPrint('WebViewOverlayService: 清理完成，包括停止可见性监控');
    } catch (e) {
      debugPrint('WebViewOverlayService: Cleanup failed - $e');
    }
  }

  // =============================================================================
  // FONT SIZE CALCULATION AND TEXT FITTING
  // =============================================================================

  /// Calculate optimal font size and potentially adjusted bounding box for translated text
  FontSizeCalculationResult _calculateOptimalFontSize({
    required String translatedText,
    required double originalWidth,
    required double originalHeight,
    double minFontSize = 9.0,
    double maxFontSize = 22.0,
    double maxExpansionFactor = 1.5,
  }) {
    debugPrint('WebViewOverlayService: Calculating font size for text: "${translatedText.length > 50 ? translatedText.substring(0, 50) + '...' : translatedText}"');
    debugPrint('WebViewOverlayService: Original dimensions: ${originalWidth}x$originalHeight');

    // Handle edge cases
    if (translatedText.isEmpty) {
      return FontSizeCalculationResult(
        fontSize: minFontSize,
        adjustedWidth: originalWidth,
        adjustedHeight: originalHeight,
        wasExpanded: false,
      );
    }

    // Estimate character dimensions (approximate values for typical fonts)
    const double avgCharWidthRatio = 0.6; // Character width is ~60% of font size
    const double lineHeightRatio = 1.2; // Line height is ~120% of font size

    // Calculate text metrics
    final int textLength = translatedText.length;
    final int estimatedLines = _estimateLineCount(translatedText, originalWidth, maxFontSize * avgCharWidthRatio);

    debugPrint('WebViewOverlayService: Text length: $textLength, estimated lines: $estimatedLines');

    // Calculate font size based on height constraint
    double fontSizeByHeight = (originalHeight / (estimatedLines * lineHeightRatio)).clamp(minFontSize, maxFontSize);

    // Calculate font size based on width constraint (for single line)
    double fontSizeByWidth = (originalWidth / (textLength * avgCharWidthRatio)).clamp(minFontSize, maxFontSize);

    // Use the more restrictive constraint
    double calculatedFontSize = [fontSizeByHeight, fontSizeByWidth].reduce((a, b) => a < b ? a : b);

    debugPrint('WebViewOverlayService: Font size by height: $fontSizeByHeight, by width: $fontSizeByWidth');
    debugPrint('WebViewOverlayService: Initial calculated font size: $calculatedFontSize');

    // Check if font size is below minimum readability threshold
    if (calculatedFontSize < minFontSize) {
      debugPrint('WebViewOverlayService: Font size below minimum, applying region expansion');
      return _expandRegionForReadability(
        translatedText: translatedText,
        originalWidth: originalWidth,
        originalHeight: originalHeight,
        minFontSize: minFontSize,
        maxExpansionFactor: maxExpansionFactor,
      );
    }

    return FontSizeCalculationResult(
      fontSize: calculatedFontSize,
      adjustedWidth: originalWidth,
      adjustedHeight: originalHeight,
      wasExpanded: false,
    );
  }

  /// Estimate the number of lines needed for text given width and character size
  int _estimateLineCount(String text, double availableWidth, double charWidth) {
    if (text.isEmpty || availableWidth <= 0 || charWidth <= 0) return 1;

    final int charsPerLine = (availableWidth / charWidth).floor();
    if (charsPerLine <= 0) return text.length; // Fallback: each character on its own line

    // Simple word wrapping estimation
    final words = text.split(' ');
    int lines = 1;
    int currentLineLength = 0;

    for (final word in words) {
      final wordLength = word.length + 1; // +1 for space
      if (currentLineLength + wordLength > charsPerLine) {
        lines++;
        currentLineLength = wordLength;
      } else {
        currentLineLength += wordLength;
      }
    }

    return lines;
  }

  /// Expand region when font size would be too small for readability
  FontSizeCalculationResult _expandRegionForReadability({
    required String translatedText,
    required double originalWidth,
    required double originalHeight,
    required double minFontSize,
    required double maxExpansionFactor,
  }) {
    debugPrint('WebViewOverlayService: Expanding region for readability');

    // Calculate required dimensions for minimum font size
    const double avgCharWidthRatio = 0.6;
    const double lineHeightRatio = 1.2;

    final int textLength = translatedText.length;

    // Calculate required width for single line at minimum font size
    final double requiredWidthSingleLine = textLength * minFontSize * avgCharWidthRatio;

    // Start with width expansion (preferred)
    double adjustedWidth = originalWidth;
    double adjustedHeight = originalHeight;
    String expansionReason = '';

    if (requiredWidthSingleLine <= originalWidth * maxExpansionFactor) {
      // Can fit in single line with width expansion
      adjustedWidth = requiredWidthSingleLine.clamp(originalWidth, originalWidth * maxExpansionFactor);
      expansionReason = 'width expanded for single line';
      debugPrint('WebViewOverlayService: Width expanded to $adjustedWidth for single line');
    } else {
      // Need multiple lines, expand both width and height
      adjustedWidth = originalWidth * maxExpansionFactor;

      // Calculate how many lines we need with expanded width
      final int charsPerLine = (adjustedWidth / (minFontSize * avgCharWidthRatio)).floor();
      final int requiredLines = (textLength / charsPerLine).ceil().clamp(1, 5); // Max 5 lines

      final double requiredHeight = requiredLines * minFontSize * lineHeightRatio;
      adjustedHeight = requiredHeight.clamp(originalHeight, originalHeight * maxExpansionFactor);

      expansionReason = 'width and height expanded for $requiredLines lines';
      debugPrint('WebViewOverlayService: Dimensions expanded to ${adjustedWidth}x$adjustedHeight for $requiredLines lines');
    }

    // Recalculate font size with adjusted dimensions
    final int estimatedLines = _estimateLineCount(translatedText, adjustedWidth, minFontSize * avgCharWidthRatio);
    final double finalFontSize = (adjustedHeight / (estimatedLines * lineHeightRatio)).clamp(minFontSize, 24.0);

    debugPrint('WebViewOverlayService: Final font size after expansion: $finalFontSize');

    return FontSizeCalculationResult(
      fontSize: finalFontSize,
      adjustedWidth: adjustedWidth,
      adjustedHeight: adjustedHeight,
      wasExpanded: true,
      expansionReason: expansionReason,
    );
  }

  /// Escape JavaScript string to prevent injection attacks
  String _escapeJavaScriptString(String text) {
    return "'" + text
        .replaceAll('\\', '\\\\')
        .replaceAll("'", "\\'")
        .replaceAll('"', '\\"')
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t') + "'";
  }

  /// Re-initialize image observation for translation
  Future<Map<String, dynamic>?> reinitializeImageObservation() async {
    const script = '''
      (function() {
        try {
          var allImages = document.querySelectorAll('img');
          var contentImages = Array.from(allImages).filter(img => {
            return window.translationOverlays && window.translationOverlays.isMainContentImage
              ? window.translationOverlays.isMainContentImage(img) : true;
          });

          var status = {
            overlaysExists: !!window.translationOverlays,
            observerExists: !!(window.translationOverlays && window.translationOverlays.intersectionObserver),
            totalImageCount: allImages.length,
            contentImageCount: contentImages.length,
            contentImageUrls: contentImages.map(function(img) {
              return img.src;
            }).slice(0, 10) // Get up to 10 image URLs
          };

          if (window.translationOverlays && window.translationOverlays.intersectionObserver) {
            // Re-observe only main content images
            var observedCount = 0;
            document.querySelectorAll('img').forEach(img => {
              if (window.translationOverlays.isMainContentImage(img)) {
                window.translationOverlays.intersectionObserver.observe(img);
                observedCount++;
              }
            });
            console.log('Translation enabled: Re-initialized image observation for', observedCount, 'content images out of', document.querySelectorAll('img').length, 'total images');

            // 启动增强的图片可见性监控（实时检测 + 定期备用）
            if (window.translationOverlays.initVisibilityMonitoring) {
              window.translationOverlays.initVisibilityMonitoring();
              console.log('增强图片可见性监控已启动（实时检测 + 定期备用）');
            }

            status.reinitialized = true;
            status.observedImageCount = observedCount;
          } else {
            console.log('Translation overlays not ready yet');
            status.reinitialized = false;
          }

          return status;
        } catch (error) {
          console.error('Error in re-initialization script:', error);
          return {
            error: error.toString(),
            overlaysExists: !!window.translationOverlays,
            observerExists: !!(window.translationOverlays && window.translationOverlays.intersectionObserver)
          };
        }
      })();
    ''';

    return await _executeJavaScriptSafely(script, operationName: 'reinitialize image observation');
  }

  /// Trigger manual image detection for testing
  Future<void> triggerManualImageDetection() async {
    const script = '''
      (function() {
        if (!window.translationOverlays || !window.translationOverlays.isMainContentImage) {
          console.log('Translation overlays not available for manual detection');
          return;
        }

        var contentImages = Array.from(document.querySelectorAll('img')).filter(img => {
          return window.translationOverlays.isMainContentImage(img);
        });

        if (contentImages.length > 0) {
          var testImage = contentImages[0];
          console.log('Manually triggering image detection for:', testImage.src);

          // Trigger the callback for the first content image
          if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
            window.flutter_inappwebview.callHandler('onImageVisible', testImage.src);
          }
        }
      })();
    ''';

    await _executeJavaScriptSimple(script, operationName: 'trigger manual image detection');
  }

  /// Get content image URLs for cache checking
  Future<List<String>> getContentImageUrls() async {
    const script = '''
      (function() {
        if (!window.translationOverlays || !window.translationOverlays.isMainContentImage) {
          return [];
        }

        var contentImages = Array.from(document.querySelectorAll('img')).filter(img => {
          return window.translationOverlays.isMainContentImage(img);
        });

        return contentImages.map(img => img.src);
      })();
    ''';

    if (_currentController == null) return [];

    try {
      final result = await _currentController!.evaluateJavascript(source: script);

      if (result is List) {
        return result.cast<String>();
      }
      return [];
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to get content image URLs - $e');
      return [];
    }
  }

  /// Dispose the service
  void dispose() {
    _currentController = null;
    _activeOverlayIds.clear();
    _overlaysActive = false;
    _isInitialized = false;
    // debugPrint('WebViewOverlayService: Disposed');
  }

  /// Create a simple overlay as a fallback method with optimized dimensions
  Future<void> _createSimpleOverlay(String overlayId, String text, double x, double y, double fontSize, {double? width, double? height}) async {
    if (_currentController == null) return;

    // Calculate default dimensions if not provided
    final overlayWidth = width ?? (text.length * fontSize * 0.6).clamp(80.0, 400.0);
    final overlayHeight = height ?? (fontSize * 1.5).clamp(20.0, 100.0);

    try {
      await _currentController!.evaluateJavascript(source: '''
        (function() {
          // 创建覆盖层元素
          const overlay = document.createElement('div');
          overlay.id = 'translation-overlay-$overlayId';
          overlay.className = 'translation-overlay';
          overlay.textContent = ${_escapeJavaScriptString(text)};
          overlay.style.position = 'absolute';
          overlay.style.left = '${x}px';
          overlay.style.top = '${y}px';
          overlay.style.width = '${overlayWidth}px';
          overlay.style.height = '${overlayHeight}px';
          overlay.style.fontSize = '${fontSize}px';
          overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
          overlay.style.color = '#333';
          overlay.style.padding = '2px 4px';
          overlay.style.borderRadius = '4px';
          overlay.style.zIndex = '10000';
          overlay.style.pointerEvents = 'none';
          overlay.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
          overlay.style.opacity = '1';
          overlay.style.maxWidth = '300px';
          overlay.style.wordWrap = 'break-word';
          overlay.style.whiteSpace = 'normal';
          
          document.body.appendChild(overlay);
          
          console.log('Simple overlay created:', overlay.id);
          return true;
        })();
      ''');
      
      debugPrint('WebViewOverlayService: Simple overlay created for $overlayId');
      _activeOverlayIds.add(overlayId);
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to create simple overlay for $overlayId: $e');
    }
  }

  /// Debug overlay positions and visibility
  Future<void> debugOverlayPositions() async {
    if (_currentController == null) return;

    try {
      final debugResult = await _currentController!.evaluateJavascript(source: '''
        (function() {
          const overlays = document.querySelectorAll('.translation-overlay');
          const viewportHeight = window.innerHeight;
          const viewportWidth = window.innerWidth;
          const scrollY = window.scrollY;
          const scrollX = window.scrollX;
          
          const overlayInfo = Array.from(overlays).map((overlay, index) => {
            const rect = overlay.getBoundingClientRect();
            const isInViewport = rect.top >= 0 && rect.bottom <= viewportHeight && 
                                rect.left >= 0 && rect.right <= viewportWidth;
            
            return {
              id: overlay.id,
              text: overlay.textContent,
              position: {
                left: overlay.style.left,
                top: overlay.style.top,
                computedLeft: rect.left,
                computedTop: rect.top
              },
              size: {
                width: rect.width,
                height: rect.height
              },
              viewport: {
                inViewport: isInViewport,
                viewportHeight: viewportHeight,
                viewportWidth: viewportWidth,
                scrollY: scrollY,
                scrollX: scrollX
              },
              styles: {
                zIndex: window.getComputedStyle(overlay).zIndex,
                opacity: window.getComputedStyle(overlay).opacity,
                display: window.getComputedStyle(overlay).display,
                visibility: window.getComputedStyle(overlay).visibility
              }
            };
          });
          
          return {
            overlayCount: overlays.length,
            overlays: overlayInfo,
            viewport: {
              height: viewportHeight,
              width: viewportWidth,
              scrollY: scrollY,
              scrollX: scrollX
            }
          };
        })();
      ''');
      
      debugPrint('WebViewOverlayService: Overlay position debug: $debugResult');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to debug overlay positions - $e');
    }
  }

  /// Debug action buttons status
  Future<void> debugActionButtons() async {
    if (_currentController == null) return;

    try {
      final debugResult = await _currentController!.evaluateJavascript(source: '''
        (function() {
          const buttons = document.querySelectorAll('.translation-action-button');
          const images = document.querySelectorAll('img');
          
          const buttonInfo = Array.from(buttons).map((button, index) => {
            const rect = button.getBoundingClientRect();
            return {
              id: button.id || 'button_' + index,
              text: button.textContent,
              position: {
                left: button.style.left,
                top: button.style.top,
                computedLeft: rect.left,
                computedTop: rect.top
              },
              size: {
                width: rect.width,
                height: rect.height
              },
              styles: {
                zIndex: window.getComputedStyle(button).zIndex,
                opacity: window.getComputedStyle(button).opacity,
                display: window.getComputedStyle(button).display,
                visibility: window.getComputedStyle(button).visibility
              },
              classes: button.className
            };
          });
          
          const imageInfo = Array.from(images).map((img, index) => {
            const rect = img.getBoundingClientRect();
            return {
              src: img.src,
              alt: img.alt,
              size: {
                width: rect.width,
                height: rect.height,
                naturalWidth: img.naturalWidth,
                naturalHeight: img.naturalHeight
              },
              position: {
                left: rect.left,
                top: rect.top,
                right: rect.right,
                bottom: rect.bottom
              }
            };
          });
          
          return {
            buttonCount: buttons.length,
            imageCount: images.length,
            buttons: buttonInfo,
            images: imageInfo,
            translationOverlaysExists: !!window.translationOverlays,
            actionButtonsMapSize: window.translationOverlays ? window.translationOverlays.actionButtons.size : 0
          };
        })();
      ''');
      
      debugPrint('WebViewOverlayService: Action buttons debug: $debugResult');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to debug action buttons - $e');
    }
  }

  /// Force show all action buttons (for debugging)
  Future<void> forceShowAllActionButtons() async {
    if (_currentController == null) return;

    try {
      debugPrint('WebViewOverlayService: Force showing all action buttons...');
      
      await _currentController!.evaluateJavascript(source: '''
        if (window.translationOverlays) {
          window.translationOverlays.showAllActionButtons();
          console.log('Force showed all action buttons');
        } else {
          console.log('translationOverlays not available');
        }
      ''');
      
      // 等待一下然后调试按钮状态
      await Future.delayed(Duration(milliseconds: 500));
      await debugActionButtons();
      
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to force show action buttons - $e');
    }
  }

  /// Debug image and overlay positioning
  Future<void> debugImageAndOverlayPositioning() async {
    if (_currentController == null) return;

    try {
      final debugResult = await _currentController!.evaluateJavascript(source: '''
        (function() {
          const images = document.querySelectorAll('img');
          const overlays = document.querySelectorAll('.translation-overlay');
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;
          
          const imageInfo = Array.from(images).map((img, index) => {
            const rect = img.getBoundingClientRect();
            return {
              src: img.src,
              index: index,
              rect: {
                left: rect.left,
                top: rect.top,
                right: rect.right,
                bottom: rect.bottom,
                width: rect.width,
                height: rect.height
              },
              absolutePosition: {
                left: rect.left + scrollX,
                top: rect.top + scrollY,
                right: rect.right + scrollX,
                bottom: rect.bottom + scrollY
              },
              naturalSize: {
                width: img.naturalWidth,
                height: img.naturalHeight
              }
            };
          });
          
          const overlayInfo = Array.from(overlays).map((overlay, index) => {
            const rect = overlay.getBoundingClientRect();
            return {
              id: overlay.id,
              index: index,
              stylePosition: {
                left: overlay.style.left,
                top: overlay.style.top
              },
              computedPosition: {
                left: rect.left,
                top: rect.top
              },
              absolutePosition: {
                left: rect.left + scrollX,
                top: rect.top + scrollY
              }
            };
          });
          
          return {
            scrollPosition: { x: scrollX, y: scrollY },
            imageCount: images.length,
            overlayCount: overlays.length,
            images: imageInfo,
            overlays: overlayInfo
          };
        })();
      ''');
      
      debugPrint('WebViewOverlayService: Image and overlay positioning debug: $debugResult');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to debug image and overlay positioning - $e');
    }
  }

  /// Debug overlay rendering state
  Future<void> debugOverlayRendering() async {
    if (_currentController == null) return;

    try {
      final debugResult = await _currentController!.evaluateJavascript(source: '''
        (function() {
          const overlays = document.querySelectorAll('.translation-overlay');
          
          const overlayInfo = Array.from(overlays).map((overlay, index) => {
            const rect = overlay.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(overlay);
            
            return {
              id: overlay.id,
              index: index,
              text: overlay.textContent,
              rect: {
                left: rect.left,
                top: rect.top,
                width: rect.width,
                height: rect.height
              },
              style: {
                left: overlay.style.left,
                top: overlay.style.top,
                width: overlay.style.width,
                height: overlay.style.height
              },
              computedStyle: {
                left: computedStyle.left,
                top: computedStyle.top,
                width: computedStyle.width,
                height: computedStyle.height,
                display: computedStyle.display,
                position: computedStyle.position,
                zIndex: computedStyle.zIndex,
                opacity: computedStyle.opacity
              },
              parent: {
                tagName: overlay.parentNode ? overlay.parentNode.tagName : 'null',
                id: overlay.parentNode ? overlay.parentNode.id : 'null'
              }
            };
          });
          
          return {
            overlayCount: overlays.length,
            overlays: overlayInfo
          };
        })();
      ''');
      
      debugPrint('WebViewOverlayService: Overlay rendering debug: $debugResult');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to debug overlay rendering - $e');
    }
  }

  /// 检查当前网站是否为已知的图片切换网站
  Future<bool> isImageSwitchingSite() async {
    const script = '''
      (function() {
        const hostname = window.location.hostname.toLowerCase();
        const knownSites = [
          'mangadex.org',
          'mangakakalot.com',
          'manganelo.com',
          'mangafreak.net',
          'readmanga.live',
          'mangareader.net'
        ];

        return knownSites.some(site => hostname.includes(site));
      })();
    ''';

    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: script);
      final isKnownSite = result == true;

      if (isKnownSite) {
        debugPrint('WebViewOverlayService: 检测到已知的图片切换网站，将启用增强检测');
      }

      return isKnownSite;
    } catch (e) {
      debugPrint('WebViewOverlayService: 检查网站类型失败 - $e');
      return false;
    }
  }
}

/// Custom exception for overlay operations
class OverlayException implements Exception {
  final String message;
  final dynamic originalError;

  const OverlayException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'OverlayException: $message (Original error: $originalError)';
    }
    return 'OverlayException: $message';
  }
}
