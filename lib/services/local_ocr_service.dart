import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:vision_text_recognition/vision_text_recognition.dart';

/// Local OCR service using vision_text_recognition package
/// Provides text recognition from images with comprehensive error handling
class LocalOcrService {
  static final LocalOcrService _instance = LocalOcrService._internal();
  factory LocalOcrService() => _instance;
  LocalOcrService._internal();

  final config = TextRecognitionConfig(
    recognitionLevel: RecognitionLevel.accurate,
    usesLanguageCorrection: true,
    automaticallyDetectsLanguage: true,
    minimumTextHeight: 0.02,
    // preferredLanguages: ['en', 'es', 'fr'],
  );

  bool _isInitialized = false;

  /// Initialize the OCR service
  /// Must be called before using any OCR functionality
  Future<void> initialize() async {
    try {
      // Check if vision text recognition is available
      final isAvailable = await VisionTextRecognition.isAvailable();
      if (!isAvailable) {
        throw OcrException('Vision text recognition is not available on this platform');
      }

      _isInitialized = true;
      debugPrint('LocalOcrService: Initialized successfully');
    } catch (e) {
      debugPrint('LocalOcrService: Failed to initialize - $e');
      throw OcrException('Failed to initialize OCR service: $e');
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Extract text from image bytes
  /// Returns a list of recognized text elements with their positions
  Future<List<OcrTextElement>> extractTextFromBytes(Uint8List imageBytes, {Size? imageSize}) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalOcrService: Starting text extraction from image bytes (${imageBytes.length} bytes)');

      // Check if the service is still available
      final isAvailable = await VisionTextRecognition.isAvailable();
      if (!isAvailable) {
        throw OcrException('Vision text recognition is not available');
      }

      // Perform text recognition using the correct API
      final recognizedText = await VisionTextRecognition.recognizeTextWithConfig(imageBytes, config);
      debugPrint('LocalOcrService: Recognition completed, full text length: ${recognizedText.fullText.length}');

      if (recognizedText.fullText.isEmpty) {
        debugPrint('LocalOcrService: No text recognized in image');
        return [];
      }

      debugPrint('LocalOcrService: Full recognized text: "${recognizedText.fullText}"');

      // Convert to our format
      final textElements = <OcrTextElement>[];

      // debugPrint('LocalOcrService: Processing ${recognizedText.textBlocks.length} text blocks');

      // Try to get image dimensions for coordinate conversion
      Size actualImageSize = imageSize ?? const Size(1.0, 1.0);
      if (imageSize == null) {
        // Try to decode image to get actual dimensions
        try {
          actualImageSize = await _getImageSizeFromBytes(imageBytes);
          debugPrint('LocalOcrService: Decoded image size from bytes: ${actualImageSize.width}x${actualImageSize.height}');
        } catch (e) {
          debugPrint('LocalOcrService: Could not decode image size from bytes: $e');
          // Use normalized coordinates as fallback
          actualImageSize = const Size(1.0, 1.0);
          debugPrint('LocalOcrService: Using normalized coordinates as fallback');
        }
      }

      for (int i = 0; i < recognizedText.textBlocks.length; i++) {
        final block = recognizedText.textBlocks[i];
        final boundingBox = block.boundingBox;

        debugPrint('LocalOcrService: Block $i - Text: "${block.text}", Confidence: ${block.confidence}');
        debugPrint('LocalOcrService: Block $i - Normalized BoundingBox: (${boundingBox.x}, ${boundingBox.y}, ${boundingBox.width}, ${boundingBox.height})');

        // Convert normalized coordinates to pixel coordinates
        final left = boundingBox.x * actualImageSize.width;
        final top = boundingBox.y * actualImageSize.height;
        final right = (boundingBox.x + boundingBox.width) * actualImageSize.width;
        final bottom = (boundingBox.y + boundingBox.height) * actualImageSize.height;

        debugPrint('LocalOcrService: Block $i - Pixel BoundingBox: ($left, $top, $right, $bottom)');

        textElements.add(OcrTextElement(
          text: block.text,
          boundingBox: OcrBoundingBox(
            left: left,
            top: top,
            right: right,
            bottom: bottom,
          ),
          confidence: block.confidence,
        ));
      }

      debugPrint('LocalOcrService: Extracted ${textElements.length} text elements');

      // Group nearby text elements into coherent sentences
      // final groupedElements = _groupTextElements(textElements);
      // debugPrint('LocalOcrService: Text grouping completed: ${groupedElements.length} grouped elements (reduced from ${textElements.length})');

      // return groupedElements;
      return textElements;
    } catch (e) {
      debugPrint('LocalOcrService: Text extraction failed - $e');
      throw OcrException('Failed to extract text from image: $e');
    }
  }

  /// Extract text from image file path
  Future<List<OcrTextElement>> extractTextFromPath(String imagePath) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalOcrService: Starting text extraction from path: $imagePath');

      // Read file as bytes
      final file = File(imagePath);
      if (!await file.exists()) {
        throw OcrException('Image file not found: $imagePath');
      }

      final imageBytes = await file.readAsBytes();

      // Use the same method as extractTextFromBytes
      return await extractTextFromBytes(imageBytes);
    } catch (e) {
      debugPrint('LocalOcrService: Text extraction failed - $e');
      throw OcrException('Failed to extract text from image: $e');
    }
  }

  /// Test OCR service with platform info
  Future<bool> testOcrService() async {
    if (!_isInitialized) {
      debugPrint('LocalOcrService: Cannot test - service not initialized');
      return false;
    }

    try {
      // Check if service is available
      final isAvailable = await VisionTextRecognition.isAvailable();
      debugPrint('LocalOcrService: Test - Service available: $isAvailable');

      if (!isAvailable) {
        return false;
      }

      // Get platform info
      final platformInfo = await VisionTextRecognition.getPlatformInfo();
      debugPrint('LocalOcrService: Test - Platform: ${platformInfo.platform}');
      debugPrint('LocalOcrService: Test - Engine: ${platformInfo.engine}');
      debugPrint('LocalOcrService: Test - Engine Version: ${platformInfo.engineVersion}');
      debugPrint('LocalOcrService: Test - Capabilities: ${platformInfo.capabilities}');

      return true;
    } catch (e) {
      debugPrint('LocalOcrService: Test failed - $e');
      return false;
    }
  }

  /// Process multiple images with OCR workflow
  Future<List<OcrTextElement>> processImagesWithOcr({
    required List<String> imageUrls,
    required dynamic extractor,
    Function(String)? onProgress,
  }) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    final allTextElements = <OcrTextElement>[];

    try {
      debugPrint('LocalOcrService: Processing ${imageUrls.length} images with OCR');

      for (int i = 0; i < imageUrls.length; i++) {
        final imageUrl = imageUrls[i];
        onProgress?.call('Processing image ${i + 1}/${imageUrls.length}: ${imageUrl.split('/').last}');

        try {
          // Download image
          Uint8List? imageBytes;
          if (extractor.needsWebViewForImages()) {
            final images = await extractor.downloadImages([imageUrl]);
            if (images.isNotEmpty) {
              imageBytes = images[0];
            }
          } else {
            imageBytes = await extractor.getImageData(imageUrl, useWebView: false);
          }

          if (imageBytes == null) {
            debugPrint("LocalOcrService: Failed to download image: $imageUrl");
            continue;
          }

          debugPrint("LocalOcrService: Successfully downloaded image: $imageUrl (${imageBytes.length} bytes)");

          // Get image size for coordinate conversion
          Size? imageSize;
          try {
            // Try to get image size from extractor if available
            imageSize = await _getImageSizeFromExtractor(imageUrl, extractor);
          } catch (e) {
            debugPrint("LocalOcrService: Could not get image size: $e");
          }

          // Extract text using OCR
          final textElements = await extractTextFromBytes(imageBytes, imageSize: imageSize);
          debugPrint("LocalOcrService: OCR processing completed for $imageUrl: ${textElements.length} text elements found");

          // Group nearby text elements into coherent sentences
          final groupedElements = _groupTextElements(textElements);
          debugPrint("LocalOcrService: Text grouping completed for $imageUrl: ${groupedElements.length} grouped elements (reduced from ${textElements.length})");

          allTextElements.addAll(groupedElements);
          // allTextElements.addAll(textElements);
        } catch (e) {
          debugPrint("LocalOcrService: Error processing image $imageUrl: $e");
          continue;
        }
      }

      debugPrint("LocalOcrService: OCR workflow completed. Total text elements: ${allTextElements.length}");
      return allTextElements;
    } catch (e) {
      debugPrint("LocalOcrService: OCR workflow failed: $e");
      throw OcrException('OCR workflow failed: $e');
    }
  }

  /// Get image size from image bytes by decoding the image
  Future<Size> _getImageSizeFromBytes(Uint8List imageBytes) async {
    try {
      // Decode the image to get its dimensions
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;

      final size = Size(image.width.toDouble(), image.height.toDouble());
      image.dispose();
      codec.dispose();

      return size;
    } catch (e) {
      debugPrint('LocalOcrService: Failed to decode image size from bytes: $e');
      throw OcrException('Failed to decode image size: $e');
    }
  }

  /// Get image size from extractor (helper method)
  Future<Size?> _getImageSizeFromExtractor(String imageUrl, dynamic extractor) async {
    try {
      debugPrint('LocalOcrService: Attempting to get image size for: $imageUrl');

      // Use JavaScript to get the actual image dimensions from the DOM
      final String script = '''
        (function() {
          const images = document.querySelectorAll('img');
          for (let i = 0; i < images.length; i++) {
            const img = images[i];
            if (img.src === '$imageUrl' || img.src.includes('${imageUrl.split('/').last}')) {
              return {
                width: img.naturalWidth || img.width || 0,
                height: img.naturalHeight || img.height || 0,
                displayWidth: img.width || 0,
                displayHeight: img.height || 0,
                src: img.src
              };
            }
          }
          return null;
        })();
      ''';

      final result = await extractor.controller.evaluateJavascript(source: script);

      if (result != null && result != 'null') {
        final Map<String, dynamic> imageInfo = Map<String, dynamic>.from(result);
        final double width = (imageInfo['width'] as num?)?.toDouble() ?? 0.0;
        final double height = (imageInfo['height'] as num?)?.toDouble() ?? 0.0;

        if (width > 0 && height > 0) {
          debugPrint('LocalOcrService: Found image size: ${width}x$height for $imageUrl');
          debugPrint('LocalOcrService: Display size: ${imageInfo['displayWidth']}x${imageInfo['displayHeight']}');
          return Size(width, height);
        } else {
          debugPrint('LocalOcrService: Invalid image dimensions: ${width}x$height');
        }
      } else {
        debugPrint('LocalOcrService: No matching image found in DOM for: $imageUrl');
      }

      return null;
    } catch (e) {
      debugPrint('LocalOcrService: Failed to get image size from extractor: $e');
      return null;
    }
  }

  /// Filter out non-content text (copyright, credits, etc.)
  List<OcrTextElement> _filterContentText(List<OcrTextElement> elements) {
    final filteredElements = <OcrTextElement>[];

    for (final element in elements) {
      final text = element.text.toLowerCase();

      // Skip copyright and credit information
      if (_isNonContentText(text)) {
        debugPrint("LocalOcrService: Filtering out non-content text: ${element.text}");
        continue;
      }

      // Skip very short text (likely noise)
      if (element.text.trim().length < 2) {
        debugPrint("LocalOcrService: Filtering out short text: ${element.text}");
        continue;
      }

      filteredElements.add(element);
    }

    debugPrint("LocalOcrService: Filtered ${elements.length - filteredElements.length} non-content elements");
    return filteredElements;
  }

  /// Check if text is non-content (copyright, credits, etc.)
  bool _isNonContentText(String text) {
    final lowerText = text.toLowerCase();

    // Common copyright and credit keywords
    final nonContentKeywords = [
      'copyright', '©', 'translator', 'typesetter', 'cleaner', 'redrawer',
      'quality check', 'proof reader', 'raw provider', 'scanlation',
      'website:', 'instagram:', 'facebook:', 'discord:', 'twitter:',
      'https://', 'http://', '.com', '.org', '.net', '.id',
      'chapter', 'vol', 'volume', 'page',
      'jangan lupa', 'kunjungi', 'sosial media',
      'themanga', 'mangadx', 'mangadex',
      'ramboo', 'comice', // Specific to this case
    ];

    // Check if text contains multiple non-content keywords
    int keywordCount = 0;
    for (final keyword in nonContentKeywords) {
      if (lowerText.contains(keyword)) {
        keywordCount++;
      }
    }

    // If text contains 2+ non-content keywords, it's likely non-content
    if (keywordCount >= 2) {
      return true;
    }

    // Check for URL patterns
    if (lowerText.contains('http') || lowerText.contains('www.') || lowerText.contains('.com')) {
      return true;
    }

    // Check for very long text (likely credits/copyright)
    if (text.length > 200) {
      return true;
    }

    return false;
  }

  /// Group nearby text elements into coherent sentences
  /// This method merges text elements that are close to each other spatially
  /// following the natural block order from OCR recognition
  List<OcrTextElement> _groupTextElements(List<OcrTextElement> elements) {
    if (elements.isEmpty) return elements;

    debugPrint('LocalOcrService: Starting block-order text grouping with ${elements.length} elements');

    // First filter out non-content text
    final contentElements = _filterContentText(elements);
    if (contentElements.isEmpty) {
      debugPrint("LocalOcrService: No content text found after filtering");
      return [];
    }

    debugPrint('LocalOcrService: Continuing with ${contentElements.length} content elements after filtering');

    // Keep elements in their original OCR block order (no sorting)
    // This preserves the natural reading order detected by the OCR engine
    final blockOrderElements = List<OcrTextElement>.from(contentElements);

    final groupedElements = <OcrTextElement>[];
    final used = <bool>[];

    // Initialize used array
    for (int i = 0; i < blockOrderElements.length; i++) {
      used.add(false);
    }

    for (int i = 0; i < blockOrderElements.length; i++) {
      if (used[i]) continue;

      final currentElement = blockOrderElements[i];
      final group = <OcrTextElement>[currentElement];
      used[i] = true;

      // Look for adjacent elements in block order to group with current element
      // Check all remaining elements to allow for complete text grouping
      for (int j = i + 1; j < blockOrderElements.length; j++) {
        if (used[j]) continue;

        final candidateElement = blockOrderElements[j];

        // Check if candidate element should be grouped with current group
        if (_shouldGroupElements(group, candidateElement)) {
          group.add(candidateElement);
          used[j] = true;
          debugPrint('LocalOcrService: Grouped block ${i} with block ${j}: "${currentElement.text}" + "${candidateElement.text}"');
        } else {
          // Debug why grouping failed
          final groupBounds = _calculateGroupBounds(group);
          final candidateBounds = candidateElement.boundingBox;
          final verticalDistance = _calculateVerticalDistance(groupBounds, candidateBounds);
          final horizontalDistance = _calculateHorizontalDistance(groupBounds, candidateBounds);
          final avgHeight = (groupBounds.height + candidateBounds.height) / 2;
          final wouldFormSentence = _wouldFormCoherentText(group, candidateElement);

          debugPrint('LocalOcrService: Cannot group block ${i} with block ${j}: "${currentElement.text}" + "${candidateElement.text}"');
          debugPrint('  - Vertical distance: ${verticalDistance.toStringAsFixed(1)} (threshold: ${(avgHeight * 0.8).toStringAsFixed(1)})');
          debugPrint('  - Horizontal distance: ${horizontalDistance.toStringAsFixed(1)} (threshold: ${(avgHeight * 2.0).toStringAsFixed(1)})');
          debugPrint('  - Average height: ${avgHeight.toStringAsFixed(1)}');
          debugPrint('  - Would form coherent text: $wouldFormSentence');

          break;
        }
      }

      // Create merged element from group (elements are already in block order)
      final mergedElement = _mergeTextElementsInOrder(group);
      groupedElements.add(mergedElement);

      final blockIndices = <int>[];
      for (int k = 0; k < blockOrderElements.length; k++) {
        if (group.contains(blockOrderElements[k])) {
          blockIndices.add(k);
        }
      }

      debugPrint('LocalOcrService: Created block-order group with ${group.length} elements (blocks ${blockIndices.join(', ')}): "${mergedElement.text}"');
    }

    debugPrint('LocalOcrService: Block-order text grouping completed. Reduced from ${elements.length} to ${groupedElements.length} elements');
    return groupedElements;
  }

  /// Check if a candidate element should be grouped with existing group
  bool _shouldGroupElements(List<OcrTextElement> group, OcrTextElement candidate) {
    if (group.isEmpty) return false;

    // Calculate the bounding box of the current group
    final groupBounds = _calculateGroupBounds(group);
    final candidateBounds = candidate.boundingBox;

    // Calculate distances and overlaps
    final verticalDistance = _calculateVerticalDistance(groupBounds, candidateBounds);
    final horizontalDistance = _calculateHorizontalDistance(groupBounds, candidateBounds);

    // Calculate average height for threshold calculations
    final avgHeight = (groupBounds.height + candidateBounds.height) / 2;

    // Grouping criteria - more restrictive for better text separation:
    // 1. Elements on the same line (small vertical distance)
    // 2. Elements in the same column (small horizontal distance and reasonable vertical gap)
    // 3. Elements that are close enough to be part of the same text block

    final isOnSameLine = verticalDistance < avgHeight * 0.3; // Within 30% of average height
    final isInSameColumn = horizontalDistance < avgHeight * 0.5 && verticalDistance < avgHeight * 1.0; // Reduced from 1.5 to 1.0
    final isNearby = verticalDistance < avgHeight * 0.6 && horizontalDistance < avgHeight * 1.5; // Reduced thresholds

    // Additional strict check: prevent grouping elements that are too far apart vertically
    final isTooFarVertically = verticalDistance > avgHeight * 3.0; // Hard limit for vertical distance

    // Additional check: ensure text makes sense when combined
    // final wouldFormSentence = _wouldFormCoherentText(group, candidate);

    final shouldGroup = (isOnSameLine || isInSameColumn || isNearby) && !isTooFarVertically;

    // Debug output for all grouping decisions
    // debugPrint('LocalOcrService: Evaluating grouping for "${candidate.text}" with group:');
    // debugPrint('  - Vertical distance: ${verticalDistance.toStringAsFixed(1)} (avgHeight: ${avgHeight.toStringAsFixed(1)})');
    // debugPrint('  - Horizontal distance: ${horizontalDistance.toStringAsFixed(1)}');
    // debugPrint('  - isOnSameLine: $isOnSameLine (< ${(avgHeight * 0.3).toStringAsFixed(1)})');
    // debugPrint('  - isInSameColumn: $isInSameColumn (hDist < ${(avgHeight * 0.5).toStringAsFixed(1)} && vDist < ${(avgHeight * 1.0).toStringAsFixed(1)})');
    // debugPrint('  - isNearby: $isNearby (vDist < ${(avgHeight * 0.6).toStringAsFixed(1)} && hDist < ${(avgHeight * 1.5).toStringAsFixed(1)})');
    // debugPrint('  - isTooFarVertically: $isTooFarVertically (> ${(avgHeight * 3.0).toStringAsFixed(1)})');
    // debugPrint('  - wouldFormSentence: $wouldFormSentence');
    // debugPrint('  - Final decision: $shouldGroup');

    if (shouldGroup) {
      debugPrint('LocalOcrService: ✓ Grouping "${candidate.text}" with existing group');
    } else {
      debugPrint('LocalOcrService: ✗ NOT grouping "${candidate.text}" with existing group');
    }

    return shouldGroup;
  }

  /// Calculate the bounding box that encompasses all elements in a group
  OcrBoundingBox _calculateGroupBounds(List<OcrTextElement> group) {
    if (group.isEmpty) {
      return const OcrBoundingBox(left: 0, top: 0, right: 0, bottom: 0);
    }

    double minLeft = group.first.boundingBox.left;
    double minTop = group.first.boundingBox.top;
    double maxRight = group.first.boundingBox.right;
    double maxBottom = group.first.boundingBox.bottom;

    for (final element in group) {
      final box = element.boundingBox;
      minLeft = minLeft < box.left ? minLeft : box.left;
      minTop = minTop < box.top ? minTop : box.top;
      maxRight = maxRight > box.right ? maxRight : box.right;
      maxBottom = maxBottom > box.bottom ? maxBottom : box.bottom;
    }

    return OcrBoundingBox(
      left: minLeft,
      top: minTop,
      right: maxRight,
      bottom: maxBottom,
    );
  }

  /// Calculate vertical distance between two bounding boxes
  double _calculateVerticalDistance(OcrBoundingBox box1, OcrBoundingBox box2) {
    // If boxes overlap vertically, distance is 0
    if (box1.bottom >= box2.top && box2.bottom >= box1.top) {
      return 0.0;
    }

    // Calculate gap between boxes
    if (box1.bottom < box2.top) {
      return box2.top - box1.bottom;
    } else {
      return box1.top - box2.bottom;
    }
  }

  /// Calculate horizontal distance between two bounding boxes
  double _calculateHorizontalDistance(OcrBoundingBox box1, OcrBoundingBox box2) {
    // If boxes overlap horizontally, distance is 0
    if (box1.right >= box2.left && box2.right >= box1.left) {
      return 0.0;
    }

    // Calculate gap between boxes
    if (box1.right < box2.left) {
      return box2.left - box1.right;
    } else {
      return box1.left - box2.right;
    }
  }

  /// Check if adding a candidate element would form coherent text
  bool _wouldFormCoherentText(List<OcrTextElement> group, OcrTextElement candidate) {
    // Enhanced heuristics for text coherence:
    // 1. Avoid grouping very different text sizes (confidence-based)
    // 2. Prefer grouping text that forms logical reading order
    // 3. Avoid grouping isolated single characters with longer text
    // 4. Detect sentence boundaries and avoid crossing them
    // 5. Avoid grouping text with very different styles (e.g., sound effects vs narrative)

    final candidateText = candidate.text.trim();
    if (candidateText.isEmpty) return false;

    // Calculate average confidence of the group
    final avgConfidence = group.map((e) => e.confidence).reduce((a, b) => a + b) / group.length;

    // Don't group if confidence is very different (might be noise)
    if ((candidate.confidence - avgConfidence).abs() > 0.3) {
      debugPrint('LocalOcrService: Rejecting grouping due to confidence difference: ${(candidate.confidence - avgConfidence).abs().toStringAsFixed(2)}');
      return false;
    }

    // Don't group single characters with multi-character text unless they're punctuation
    final groupHasMultiChar = group.any((e) => e.text.trim().length > 1);
    final candidateIsSingleChar = candidateText.length == 1;
    final punctuationChars = '.,!?;:()"\'-';
    final candidateIsPunctuation = candidateIsSingleChar && punctuationChars.contains(candidateText);

    if (groupHasMultiChar && candidateIsSingleChar && !candidateIsPunctuation) {
      debugPrint('LocalOcrService: Rejecting grouping of single character "${candidateText}" with multi-character text');
      return false;
    }

    // Check for sentence boundaries - don't group across strong sentence endings
    final lastGroupText = group.last.text.trim();
    final sentenceEnders = ['...', '.', '!', '?'];
    final hasStrongSentenceEnding = sentenceEnders.any((ender) => lastGroupText.endsWith(ender));

    if (hasStrongSentenceEnding) {
      // Allow grouping only if the candidate starts with lowercase or is clearly a continuation
      final candidateStartsLower = candidateText.isNotEmpty && candidateText[0].toLowerCase() == candidateText[0] && candidateText[0] != candidateText[0].toUpperCase();
      final isContinuation = candidateText.startsWith('-') || candidateText.startsWith('—');

      if (!candidateStartsLower && !isContinuation) {
        debugPrint('LocalOcrService: Rejecting grouping across sentence boundary: "${lastGroupText}" -> "${candidateText}"');
        return false;
      }
    }

    // Check for sound effects or special text patterns that shouldn't be grouped with narrative
    final soundEffectPatterns = [
      RegExp(r'^[A-Z]+!+$'), // All caps with exclamation (THUMP!)
      RegExp(r'^[A-Z]{2,}$'), // All caps words (might be sound effects)
      RegExp(r'.*[!]{2,}.*'), // Multiple exclamations
    ];

    final candidateIsSoundEffect = soundEffectPatterns.any((pattern) => pattern.hasMatch(candidateText));
    final groupHasNarrative = group.any((e) {
      final text = e.text.trim();
      return text.length > 3 && !soundEffectPatterns.any((pattern) => pattern.hasMatch(text));
    });

    if (candidateIsSoundEffect && groupHasNarrative) {
      debugPrint('LocalOcrService: Rejecting grouping of sound effect "${candidateText}" with narrative text');
      return false;
    }

    return true;
  }

  /// Merge multiple text elements into a single element preserving their order
  /// Elements are assumed to be already in the correct block order
  OcrTextElement _mergeTextElementsInOrder(List<OcrTextElement> elements) {
    if (elements.isEmpty) {
      throw ArgumentError('Cannot merge empty list of elements');
    }

    if (elements.length == 1) {
      return elements.first;
    }

    // Elements are already in block order, no need to sort
    // Combine text with appropriate spacing
    final textParts = <String>[];
    for (int i = 0; i < elements.length; i++) {
      final currentText = elements[i].text.trim();
      if (currentText.isEmpty) continue;

      if (textParts.isNotEmpty) {
        final previousElement = elements[i - 1];
        final currentElement = elements[i];

        // Determine if we need a space between elements
        final needsSpace = _needsSpaceBetweenElements(previousElement, currentElement);
        if (needsSpace) {
          textParts.add(' ');
        }
      }

      textParts.add(currentText);
    }

    final mergedText = textParts.join('');

    // Calculate merged bounding box
    final mergedBounds = _calculateGroupBounds(elements);

    // Calculate average confidence
    final avgConfidence = elements.map((e) => e.confidence).reduce((a, b) => a + b) / elements.length;

    return OcrTextElement(
      text: mergedText,
      boundingBox: mergedBounds,
      confidence: avgConfidence,
    );
  }



  /// Determine if space is needed between two text elements
  bool _needsSpaceBetweenElements(OcrTextElement previous, OcrTextElement current) {
    final prevText = previous.text.trim();
    final currText = current.text.trim();

    if (prevText.isEmpty || currText.isEmpty) return false;

    // Don't add space if previous text ends with punctuation that doesn't need space
    final openingPunctuation = '(["\'\`';
    if (openingPunctuation.contains(prevText[prevText.length - 1])) return false;

    // Don't add space if current text starts with punctuation that doesn't need space
    final closingPunctuation = '.,!?;:)]\"\'\`';
    if (closingPunctuation.contains(currText[0])) return false;

    // Add space for normal text
    return true;
  }

  /// Dispose resources
  Future<void> dispose() async {
    _isInitialized = false;
    debugPrint('LocalOcrService: Disposed');
  }
}

/// Represents a text element recognized by OCR
class OcrTextElement {
  final String text;
  final OcrBoundingBox boundingBox;
  final double confidence;

  const OcrTextElement({
    required this.text,
    required this.boundingBox,
    required this.confidence,
  });

  @override
  String toString() {
    return 'OcrTextElement(text: "$text", boundingBox: $boundingBox, confidence: $confidence)';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'boundingBox': boundingBox.toJson(),
      'confidence': confidence,
    };
  }

  /// Create from JSON
  factory OcrTextElement.fromJson(Map<String, dynamic> json) {
    return OcrTextElement(
      text: json['text'] as String,
      boundingBox: OcrBoundingBox.fromJson(json['boundingBox'] as Map<String, dynamic>),
      confidence: (json['confidence'] as num).toDouble(),
    );
  }
}

/// Represents a bounding box for OCR text
class OcrBoundingBox {
  final double left;
  final double top;
  final double right;
  final double bottom;

  const OcrBoundingBox({
    required this.left,
    required this.top,
    required this.right,
    required this.bottom,
  });

  /// Get width of the bounding box
  double get width => right - left;

  /// Get height of the bounding box
  double get height => bottom - top;

  /// Get center point of the bounding box
  Offset get center => Offset(left + width / 2, top + height / 2);

  /// Convert to Rect
  Rect toRect() => Rect.fromLTRB(left, top, right, bottom);

  @override
  String toString() {
    return 'OcrBoundingBox(left: $left, top: $top, right: $right, bottom: $bottom)';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'left': left,
      'top': top,
      'right': right,
      'bottom': bottom,
    };
  }

  /// Create from JSON
  factory OcrBoundingBox.fromJson(Map<String, dynamic> json) {
    return OcrBoundingBox(
      left: (json['left'] as num).toDouble(),
      top: (json['top'] as num).toDouble(),
      right: (json['right'] as num).toDouble(),
      bottom: (json['bottom'] as num).toDouble(),
    );
  }
}

/// Custom exception for OCR operations
class OcrException implements Exception {
  final String message;
  final dynamic originalError;

  const OcrException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'OcrException: $message (Original error: $originalError)';
    }
    return 'OcrException: $message';
  }
}
