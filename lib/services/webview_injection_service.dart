import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// WebView 注入服务
/// 统一管理所有 JavaScript 和 CSS 注入
class WebViewInjectionService {
  static final WebViewInjectionService _instance = WebViewInjectionService._internal();
  factory WebViewInjectionService() => _instance;
  WebViewInjectionService._internal();

  // 注入历史记录，避免重复注入
  final Set<String> _injectedResources = {};

  /// 注入 CSS 样式
  Future<bool> injectCss(
    InAppWebViewController controller,
    String cssContent, {
    String styleId = 'injected-styles',
  }) async {
    final resourceKey = 'css_$styleId';
    if (_injectedResources.contains(resourceKey)) {
      return true; // 已经注入过
    }

    try {
      final injectionScript = await _loadCssInjectionTemplate();
      final script = injectionScript
          .replaceAll('__STYLE_ID_PLACEHOLDER__', styleId)
          .replaceAll('__CSS_CONTENT_PLACEHOLDER__', cssContent);

      final result = await controller.evaluateJavascript(source: script);
      _injectedResources.add(resourceKey);
      return result != null;
    } catch (e) {
      print('Failed to inject CSS: $e');
      return false;
    }
  }

  /// 注入 JavaScript 代码
  Future<bool> injectJavaScript(
    InAppWebViewController controller,
    String jsContent, {
    String namespace = 'injectedScripts',
  }) async {
    final resourceKey = 'js_$namespace';
    if (_injectedResources.contains(resourceKey)) {
      print('WebViewInjectionService: Resource $resourceKey already injected, skipping');
      return true; // 已经注入过
    }

    try {
      final injectionScript = await _loadJsInjectionTemplate();
      
      // 对于 translationOverlays，强制重新注入，不管对象是否已经存在
      String script;
      if (namespace == 'translationOverlays') {
        script = '''
          (function() {
            try {
              __JS_CONTENT_PLACEHOLDER__
              return { success: true, message: 'Scripts injected successfully' };
            } catch (error) {
              return { success: false, error: error.toString() };
            }
          })();
        ''';
        script = script.replaceAll('__JS_CONTENT_PLACEHOLDER__', jsContent);
      } else {
        script = injectionScript
            .replaceAll('__NAMESPACE_PLACEHOLDER__', namespace)
            .replaceAll('__JS_CONTENT_PLACEHOLDER__', jsContent);
      }

      print('WebViewInjectionService: Injecting JavaScript for namespace: $namespace');
      print('WebViewInjectionService: Script length: ${script.length}');
      // print('WebViewInjectionService: Script preview: ${script.substring(0, script.length > 200 ? 200 : script.length)}...');
      final result = await controller.evaluateJavascript(source: script);
      print('WebViewInjectionService: Injection result: $result');
      
      _injectedResources.add(resourceKey);
      return result != null;
    } catch (e) {
      print('WebViewInjectionService: Failed to inject JavaScript: $e');
      return false;
    }
  }

  /// 加载 CSS 注入模板
  Future<String> _loadCssInjectionTemplate() async {
    try {
      return await rootBundle.loadString('assets/js/templates/css_injection.js');
    } catch (e) {
      // 如果模板文件不存在，使用内联模板
      return '''
        (function() {
          try {
            if (!document.getElementById('__STYLE_ID_PLACEHOLDER__')) {
              document.head.insertAdjacentHTML('beforeend', `__CSS_CONTENT_PLACEHOLDER__`);
              return { success: true, message: 'Styles injected successfully' };
            } else {
              return { success: true, message: 'Styles already exist' };
            }
          } catch (error) {
            return { success: false, error: error.toString() };
          }
        })();
      ''';
    }
  }

  /// 加载 JavaScript 注入模板
  Future<String> _loadJsInjectionTemplate() async {
    try {
      return await rootBundle.loadString('assets/js/templates/js_injection.js');
    } catch (e) {
      // 如果模板文件不存在，使用内联模板
      return '''
        (function() {
          try {
            if (!window.__NAMESPACE_PLACEHOLDER__) {
              __JS_CONTENT_PLACEHOLDER__
              return { success: true, message: 'Scripts injected successfully' };
            } else {
              return { success: true, message: 'Scripts already exist' };
            }
          } catch (error) {
            return { success: false, error: error.toString() };
          }
        })();
      ''';
    }
  }

  /// 清除注入历史
  void clearInjectionHistory() {
    _injectedResources.clear();
  }

  /// 检查是否已注入
  bool isInjected(String resourceKey) {
    return _injectedResources.contains(resourceKey);
  }

  /// 获取注入历史
  Set<String> getInjectionHistory() {
    return Set.from(_injectedResources);
  }

  /// 移除特定注入记录
  void removeInjectionRecord(String resourceKey) {
    _injectedResources.remove(resourceKey);
  }

  /// 批量注入多个资源
  Future<Map<String, bool>> injectMultipleResources(
    InAppWebViewController controller,
    Map<String, String> resources, {
    Map<String, String> cssResources = const {},
  }) async {
    final results = <String, bool>{};

    // 注入 CSS 资源
    for (final entry in cssResources.entries) {
      final success = await injectCss(
        controller,
        entry.value,
        styleId: entry.key,
      );
      results['css_${entry.key}'] = success;
    }

    // 注入 JavaScript 资源
    for (final entry in resources.entries) {
      final success = await injectJavaScript(
        controller,
        entry.value,
        namespace: entry.key,
      );
      results['js_${entry.key}'] = success;
    }

    return results;
  }

  /// 检查 WebView 是否准备好执行 JavaScript
  Future<bool> isWebViewReady(InAppWebViewController controller) async {
    try {
      final result = await controller.evaluateJavascript(source: '''
        (function() {
          return {
            documentReady: !!document,
            bodyExists: !!document.body,
            headExists: !!document.head
          };
        })();
      ''');
      
      return result != null;
    } catch (e) {
      return false;
    }
  }

  /// 等待 WebView 准备就绪
  Future<bool> waitForWebViewReady(
    InAppWebViewController controller, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    final startTime = DateTime.now();
    
    while (DateTime.now().difference(startTime) < timeout) {
      if (await isWebViewReady(controller)) {
        return true;
      }
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    return false;
  }
} 