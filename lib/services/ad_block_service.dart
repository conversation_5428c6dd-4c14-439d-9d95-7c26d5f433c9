import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AdBlockService {
  static const String _easyListUrl = 'https://easylist.to/easylist/easylist.txt';
  static const String _localEasyListPath = 'assets/easylist.txt';
  static const String _lastUpdateKey = 'ad_block_last_update';
  static const Duration _updateInterval = Duration(days: 7); // 每7天更新一次
  
  List<String> _adBlockRules = [];
  bool _isInitialized = false;
  
  // 单例模式
  static final AdBlockService _instance = AdBlockService._internal();
  
  factory AdBlockService() {
    return _instance;
  }
  
  AdBlockService._internal();
  
  // 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // 首先加载本地资源中的规则
    await _loadLocalRules();
    
    // 检查是否需要从远程更新
    if (await _shouldUpdate()) {
      // 异步更新，不阻塞UI
      _updateRulesFromRemote().then((_) {
        debugPrint('广告拦截规则已从远程更新');
      }).catchError((error) {
        debugPrint('更新广告拦截规则失败: $error');
      });
    }
    
    _isInitialized = true;
  }
  
  // 从本地资源加载规则
  Future<void> _loadLocalRules() async {
    try {
      // 首先尝试从应用文档目录读取之前下载的规则
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/easylist.txt');
      
      if (await file.exists()) {
        final content = await file.readAsString();
        _parseRules(content);
        debugPrint('从本地文件加载了 ${_adBlockRules.length} 条广告拦截规则');
      } else {
        // 如果本地文件不存在，则从应用资源加载
        final content = await rootBundle.loadString(_localEasyListPath);
        _parseRules(content);
        debugPrint('从应用资源加载了 ${_adBlockRules.length} 条广告拦截规则');
        
        // 将资源文件保存到本地
        await file.writeAsString(content);
      }
    } catch (e) {
      debugPrint('加载本地广告拦截规则失败: $e');
      // 加载失败时使用内置的基本规则
      _loadFallbackRules();
    }
  }
  
  // 从远程更新规则
  Future<void> _updateRulesFromRemote() async {
    try {
      final response = await http.get(Uri.parse(_easyListUrl));
      
      if (response.statusCode == 200) {
        final content = response.body;
        
        // 解析新规则
        _parseRules(content);
        
        // 保存到本地文件
        final directory = await getApplicationDocumentsDirectory();
        final file = File('${directory.path}/easylist.txt');
        await file.writeAsString(content);
        
        // 更新最后更新时间
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
        
        debugPrint('成功从远程更新广告拦截规则，共 ${_adBlockRules.length} 条');
      } else {
        throw Exception('HTTP错误: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('从远程更新广告拦截规则失败: $e');
      rethrow;
    }
  }
  
  // 检查是否需要更新规则
  Future<bool> _shouldUpdate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdate = prefs.getInt(_lastUpdateKey) ?? 0;
      final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdate);
      final now = DateTime.now();
      
      return now.difference(lastUpdateTime) > _updateInterval;
    } catch (e) {
      debugPrint('检查更新时间失败: $e');
      return true; // 出错时默认更新
    }
  }
  
  // 解析EasyList规则
  void _parseRules(String content) {
    final lines = content.split('\n');
    final rules = <String>[];
    
    for (var line in lines) {
      line = line.trim();
      
      // 跳过注释和空行
      if (line.isEmpty || line.startsWith('!') || line.startsWith('[')) {
        continue;
      }
      
      // 只保留网址过滤规则，忽略元素隐藏规则
      if (!line.contains('##') && !line.contains('#@#')) {
        rules.add(line);
      }
    }
    
    _adBlockRules = rules;
  }
  
  // 加载基本的内置规则（当本地和远程都失败时）
  void _loadFallbackRules() {
    _adBlockRules = [
      '||doubleclick.net^',
      '||googleadservices.com^',
      '||googlesyndication.com^',
      '||google-analytics.com^',
      '||adservice.google.com^',
      '||pagead2.googlesyndication.com^',
      '||ads.pubmatic.com^',
      '||securepubads.g.doubleclick.net^',
      '||taboola.com^',
      '||outbrain.com^',
      '||criteo.com^',
      '||adroll.com^',
      '||scorecardresearch.com^',
      '||facebook.com/tr',
      '||amazon-adsystem.com^',
      '||adnxs.com^',
      '||openx.net^',
      '||rubiconproject.com^',
    ];
    debugPrint('使用内置的基本广告拦截规则，共 ${_adBlockRules.length} 条');
  }
  
  // 检查URL是否应该被拦截
  bool shouldBlockUrl(String url, String host, String path) {
    if (!_isInitialized) {
      debugPrint('广告拦截服务尚未初始化');
      return false;
    }
    
    for (final rule in _adBlockRules) {
      if (_matchesAdRule(rule, url, host, path)) {
        return true;
      }
    }
    
    return false;
  }
  
  // 匹配广告规则的辅助方法
  bool _matchesAdRule(String rule, String url, String host, String path) {
    if (rule.startsWith('||')) {
      // 域名精确匹配规则 (||example.com^)
      final domain = rule.substring(2);
      final domainPart = domain.endsWith('^') 
          ? domain.substring(0, domain.length - 1) 
          : domain;
      
      return host == domainPart || host.endsWith('.$domainPart');
    } else if (rule.startsWith('|')) {
      // 起始匹配规则 (|http://example)
      final pattern = rule.substring(1);
      return url.startsWith(pattern);
    } else if (rule.startsWith('/') && rule.endsWith('/')) {
      // 路径匹配规则 (/ads/)
      return path.contains(rule);
    } else if (rule.contains('*')) {
      // 通配符规则 (*banner*)
      final parts = rule.split('*');
      bool matches = true;
      
      int lastIndex = 0;
      for (final part in parts) {
        if (part.isEmpty) continue;
        
        final index = url.indexOf(part, lastIndex);
        if (index == -1) {
          matches = false;
          break;
        }
        
        lastIndex = index + part.length;
      }
      
      return matches;
    } else {
      // 简单包含规则
      return url.contains(rule);
    }
  }

  bool isSearchEngineDomain(String host) {
    const searchDomains = {
      'www.google.com',
      'www.gstatic.com',
      'gstatic.com',
      'www.bing.com',
      'www.baidu.com',
      'm.baidu.com',
      'img.baidu.com',
      'www.baiduimg.cn',
      'search.yahoo.co.jp',
      'yimg.jp',
      'search.naver.com',
      'ssl.pstatic.net',
    };

    return searchDomains.any((d) => host == d || host.endsWith('.' + d.replaceFirst('www.', '')));
  }
}