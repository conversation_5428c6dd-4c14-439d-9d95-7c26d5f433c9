import 'package:flutter/material.dart';
import 'package:imtrans/services/translation_workflow_service.dart';
import 'package:google_mlkit_translation/google_mlkit_translation.dart';

/// Helper class for managing language model downloads
class LanguageDownloadHelper {
  static final TranslationWorkflowService _translationWorkflow = TranslationWorkflowService();

  /// Pre-download common language models with user feedback
  static Future<void> preDownloadCommonLanguagesWithDialog(BuildContext context) async {
    // Show confirmation dialog
    final shouldDownload = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Language Models'),
        content: const Text(
          'Would you like to download common language models (Japanese, Korean, Chinese, English) for offline translation?\n\n'
          'This will improve translation speed and work without internet connection.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Download'),
          ),
        ],
      ),
    );

    if (shouldDownload != true) return;

    // Show progress dialog
    if (!context.mounted) return;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const _DownloadProgressDialog(),
    );

    try {
      final results = await _translationWorkflow.preDownloadCommonLanguages(
        context: context,
        onProgress: (message) {
          debugPrint('Language download progress: $message');
        },
      );

      // Close progress dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show results
      final successCount = results.values.where((success) => success).length;
      final totalCount = results.length;

      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(successCount == totalCount ? 'Download Complete' : 'Download Partially Complete'),
            content: Text(
              successCount == totalCount
                  ? 'All $totalCount language models downloaded successfully!'
                  : '$successCount out of $totalCount language models downloaded successfully.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }

    } catch (e) {
      // Close progress dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error dialog
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Download Failed'),
            content: Text('Failed to download language models: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// Download specific languages with user feedback
  static Future<void> downloadSpecificLanguagesWithDialog(
    BuildContext context,
    List<TranslateLanguage> languages,
  ) async {
    if (languages.isEmpty) return;

    // Show progress dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const _DownloadProgressDialog(),
    );

    try {
      final results = await _translationWorkflow.downloadLanguageModels(
        languages,
        context: context,
        onProgress: (message) {
          debugPrint('Language download progress: $message');
        },
      );

      // Close progress dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show results
      final successCount = results.values.where((success) => success).length;
      final totalCount = results.length;

      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(successCount == totalCount ? 'Download Complete' : 'Download Partially Complete'),
            content: Text(
              successCount == totalCount
                  ? 'All $totalCount language models downloaded successfully!'
                  : '$successCount out of $totalCount language models downloaded successfully.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }

    } catch (e) {
      // Close progress dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Show error dialog
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Download Failed'),
            content: Text('Failed to download language models: $e'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// Check if common languages are downloaded
  static Future<Map<String, bool>> checkCommonLanguagesStatus() async {
    try {
      final commonLanguages = [
        TranslateLanguage.japanese,
        TranslateLanguage.korean,
        TranslateLanguage.chinese,
        TranslateLanguage.english,
      ];

      final status = <String, bool>{};
      for (final language in commonLanguages) {
        final isDownloaded = await _translationWorkflow.isLanguageDownloaded(language);
        status[language.bcpCode] = isDownloaded;
      }

      return status;
    } catch (e) {
      debugPrint('Failed to check language status: $e');
      return {};
    }
  }
}

/// Progress dialog for language downloads
class _DownloadProgressDialog extends StatelessWidget {
  const _DownloadProgressDialog();

  @override
  Widget build(BuildContext context) {
    return const AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Downloading language models...'),
          SizedBox(height: 8),
          Text(
            'This may take a few minutes depending on your internet connection.',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}
