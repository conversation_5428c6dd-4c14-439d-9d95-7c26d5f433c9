import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:imtrans/models/browser_tab.dart';
import 'package:imtrans/services/ad_block_service.dart';
import 'package:imtrans/services/event_log.dart';
import 'package:imtrans/services/browser_history_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:imtrans/pages/browser/recommended_sites_view.dart';

class BrowserTabView extends StatefulWidget {
  final BrowserTab tab;
  final Function(BrowserTab) onTabUpdated;
  final String? initialInput;
  final VoidCallback? onPageLoadComplete;

  const BrowserTabView({
    Key? key,
    required this.tab,
    required this.onTabUpdated,
    this.initialInput,
    this.onPageLoadComplete,
  }) : super(key: key);

  @override
  State<BrowserTabView> createState() => BrowserTabViewState();
}

class BrowserTabViewState extends State<BrowserTabView> with AutomaticKeepAliveClientMixin {
  final AdBlockService _adBlockService = AdBlockService();
  bool _isAdBlockingEnabled = true;
  String? _pendingInitialInput;

  // 是否显示推荐站点视图
  bool _showRecommended = false;
  
  // 重定向计数器，防止无限重定向
  int _redirectCount = 0;
  static const int _maxRedirects = 10;
  String? _lastUrl;

  InAppWebViewSettings options = InAppWebViewSettings(
    useShouldOverrideUrlLoading: true,
    mediaPlaybackRequiresUserGesture: true, // 禁用自动播放
    javaScriptEnabled: true,
    allowsInlineMediaPlayback: true,
    // 使用通用的桌面用户代理以获得更好的兼容性
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
    cacheEnabled: true,
    clearCache: false,
    // 允许混合内容
    mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
    // 禁用缩放
    supportZoom: false,
    // 设置视口
    useWideViewPort: true,
    loadWithOverviewMode: true,
    // 添加更多设置来处理复杂网站
    javaScriptCanOpenWindowsAutomatically: false,
    allowsBackForwardNavigationGestures: true,
    // 设置更长的超时时间
    resourceCustomSchemes: [],
    // 允许文件访问
    allowFileAccess: true,
    allowFileAccessFromFileURLs: true,
    allowUniversalAccessFromFileURLs: true,
  );

  @override
  void initState() {
    super.initState();
    
    // 关闭调试日志
    PlatformInAppWebViewController.debugLoggingSettings.enabled = false;
    
    // 初始化广告拦截服务
    _adBlockService.initialize();
    
    // 加载广告拦截设置
    _loadAdBlockingSettings();
    
    // 处理初始输入
    if (widget.initialInput != null && widget.initialInput!.isNotEmpty) {
      _pendingInitialInput = widget.initialInput!.trim();
    } else if (widget.tab.url.isNotEmpty) {
      // 如果没有初始输入但tab有URL（恢复的tab），使用tab的URL
      _pendingInitialInput = widget.tab.url.trim();
      debugPrint('恢复tab URL: ${widget.tab.url}');
    }

    // 如果没有初始输入且当前标签页 URL 为空，则展示推荐站点
    _showRecommended = (widget.initialInput == null || widget.initialInput!.isEmpty) && widget.tab.url.isEmpty;
  }

  // 加载广告拦截设置
  Future<void> _loadAdBlockingSettings() async {
    final prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        _isAdBlockingEnabled = prefs.getBool('ad_blocking_enabled') ?? true;
      });
    }
  }

  // 获取搜索URL，根据地区选择合适的搜索引擎
  String _getSearchUrl(String encodedQuery) {
    final locale = Localizations.localeOf(context);
    
    switch (locale.languageCode) {
      case 'zh':
        return 'https://www.baidu.com/s?wd=$encodedQuery';
      case 'ja':
        return 'https://search.yahoo.co.jp/search?p=$encodedQuery';
      case 'ko':
        return 'https://search.naver.com/search.naver?query=$encodedQuery';
      default:
        return 'https://www.google.com/search?q=$encodedQuery';
    }
  }

  // 判断输入的文本是否为有效的URL
  bool _isValidUrl(String text) {
    if (text.contains(' ')) {
      return false;
    }
    
    if (text.startsWith('http://') || text.startsWith('https://') || text.startsWith('ftp://')) {
      return true;
    }
    
    if (text.startsWith('localhost') || text == 'localhost') {
      return true;
    }
    
    final ipPattern = RegExp(
      r'^((\d{1,3}\.){3}\d{1,3})(:\d+)?$',
      caseSensitive: false,
    );
    if (ipPattern.hasMatch(text)) {
      return true;
    }
    
    final domainPattern = RegExp(
      r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$',
      caseSensitive: false,
    );
    
    final commonTlds = [
      'com', 'org', 'net', 'edu', 'gov', 'mil', 'int', 'co', 'io', 'me', 'tv', 
      'info', 'biz', 'name', 'pro', 'museum', 'aero', 'coop', 'jobs', 'travel', 
      'mobi', 'asia', 'cat', 'tel', 'xxx', 'post', 'geo', 'local', 'cn', 'jp', 
      'kr', 'uk', 'de', 'fr', 'it', 'es', 'ru', 'br', 'au', 'ca'
    ];
    
    if (domainPattern.hasMatch(text)) {
      final parts = text.split('.');
      if (parts.length >= 2) {
        final tld = parts.last.toLowerCase();
        return commonTlds.contains(tld);
      }
    }
    
    return false;
  }

  // 加载URL
  Future<void> loadUrl(String inputText) async {
    if (inputText.isEmpty) {
      debugPrint('❌ loadUrl: 输入为空');
      return;
    }
    
    // 重置重定向计数器
    _redirectCount = 0;
    _lastUrl = null;
    // debugPrint('🔄 重置重定向计数器');
    
    String finalUrl;
    
    try {
      if (_isValidUrl(inputText)) {
        if (!inputText.startsWith('http://') && !inputText.startsWith('https://')) {
          finalUrl = 'https://$inputText';
        } else {
          finalUrl = inputText;
        }
        // debugPrint('加载URL: $finalUrl');
      } else {
        final encodedQuery = Uri.encodeComponent(inputText);
        finalUrl = _getSearchUrl(encodedQuery);
        // debugPrint('搜索关键词: $inputText -> $finalUrl');
      }
      
      final uri = Uri.tryParse(finalUrl);
      if (uri == null || !uri.hasScheme) {
        debugPrint('无效的URL: $finalUrl');
        return;
      }
      
      if (widget.tab.controller != null && !widget.tab.isDisposed) {
        // debugPrint('📱 开始加载URL到WebView: $finalUrl');
        await widget.tab.controller!.loadUrl(
          urlRequest: URLRequest(url: WebUri(finalUrl))
        );
        // debugPrint('✅ URL加载请求已发送');
      } else {
        debugPrint('WebView controller不可用或已dispose，无法加载URL: $finalUrl');
        // 如果controller不可用，尝试延迟重试
        if (!widget.tab.isDisposed) {
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted && widget.tab.controller != null && !widget.tab.isDisposed) {
              try {
                debugPrint('重试加载URL: $finalUrl');
                widget.tab.controller!.loadUrl(
                  urlRequest: URLRequest(url: WebUri(finalUrl))
                );
              } catch (e) {
                debugPrint('重试加载URL失败: $e');
              }
            }
          });
        }
        return;
      }
      
      // 记录事件
      EventLogService.logWebsiteVisit(
        url: finalUrl, 
        method: _isValidUrl(inputText) ? 'manual_input' : 'search'
      );
    } catch (e) {
      debugPrint('加载URL时出错: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Load failed: ${e.toString()}'))
        );
      }
    }

    // 一旦开始加载地址，则隐藏推荐站点
    if (_showRecommended) {
      setState(() {
        _showRecommended = false;
      });
    }
  }

  Future<NavigationActionPolicy> _shouldBlockAd(NavigationAction navigationAction) async {
    var uri = navigationAction.request.url!;
    final String urlString = uri.toString().toLowerCase();
    final String host = uri.host.toLowerCase();

    // debugPrint('🛡️ 广告拦截检查: $host');

    // 如果属于搜索引擎域名，直接放行所有资源，避免白屏
    if (_adBlockService.isSearchEngineDomain(host)) {
      return NavigationActionPolicy.ALLOW;
    }

    // 使用AdBlockService检查URL
    if (_adBlockService.shouldBlockUrl(urlString, host, uri.path.toLowerCase())) {
      debugPrint('🚫 广告拦截器阻止: $urlString');
      return NavigationActionPolicy.CANCEL;
    }

    // debugPrint('✅ 广告拦截检查通过: $host');
    return NavigationActionPolicy.ALLOW;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 维持 keep-alive 状态
    return Stack(
      children: [
        InAppWebView(
          key: widget.tab.webViewKey,
          initialSettings: options,
          initialUrlRequest: null,
          onWebViewCreated: (controller) {
            // 更新tab的controller
            final updatedTab = widget.tab.copyWith(controller: controller);
            widget.onTabUpdated(updatedTab);
            
            // WebView 创建完成后，延迟加载待处理的初始输入，确保controller完全初始化
            if (_pendingInitialInput != null) {
              final initialInput = _pendingInitialInput!;
              _pendingInitialInput = null;
              
              // 延迟加载，确保WebView完全初始化
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted && widget.tab.controller != null && !widget.tab.isDisposed) {
                  try {
                    loadUrl(initialInput);
                  } catch (e) {
                    debugPrint('延迟加载初始URL失败: $e');
                  }
                }
              });
            }
          },
          onLoadStart: (controller, url) {
            if (url != null && mounted) {
              // 如果是新的URL，重置重定向计数器
              final urlString = url.toString();
              // debugPrint('🔄 onLoadStart: $urlString');
              // debugPrint('📊 当前重定向计数: $_redirectCount, 上次URL: $_lastUrl');
              
              if (_lastUrl != urlString) {
                _redirectCount = 0;
                _lastUrl = urlString;
                // debugPrint('✅ 重置重定向计数器，新URL: $urlString');
              } else {
                // debugPrint('⚠️ 相同URL重复加载: $urlString');
              }
              
              final updatedTab = widget.tab.copyWith(
                isLoading: true,
                url: urlString,
              );
              widget.onTabUpdated(updatedTab);
            }
          },
          onLoadStop: (controller, url) {
            if (url != null && mounted) {
              final urlString = url.toString();
              // debugPrint('✅ onLoadStop: $urlString');
              // debugPrint('📊 最终重定向计数: $_redirectCount');

              final updatedTab = widget.tab.copyWith(
                isLoading: false,
                url: urlString,
              );
              widget.onTabUpdated(updatedTab);

              // Notify parent that page load is complete
              widget.onPageLoadComplete?.call();
            }
          },
          onTitleChanged: (controller, title) {
            if (title != null && mounted) {
              final updatedTab = widget.tab.copyWith(title: title);
              widget.onTabUpdated(updatedTab);

              // Record in browser history
              BrowserHistoryService.addHistoryItem(
                url: widget.tab.url,
                title: title,
                favicon: widget.tab.favicon,
                enableFaviconFetch: true, // 启用favicon获取
              );
            }
          },
          onReceivedError: (controller, request, error) {
            debugPrint('WebView error: ${error.toString()}');
            debugPrint('Failed URL: ${request.url}');
            
            if (mounted) {
              final updatedTab = widget.tab.copyWith(isLoading: false);
              widget.onTabUpdated(updatedTab);
              
              // 过滤掉取消操作的错误（用户点击停止按钮）
              final errorDescription = error.description.toLowerCase();
              if (errorDescription.contains('cancelled') || 
                  errorDescription.contains('error -999') ||
                  error.type == WebResourceErrorType.CANCELLED) {
                debugPrint('请求被取消，这是正常操作');
                return;
              }
              
              // 过滤掉网络连接错误，避免过多提示
              if (errorDescription.contains('network') || 
                  errorDescription.contains('connection') ||
                  errorDescription.contains('timeout')) {
                debugPrint('网络连接错误: ${error.description}');
                return;
              }
              
              if (request.url.toString().contains('search?q=') || 
                  request.url.toString().contains('s?wd=') ||
                  request.url.toString().contains('search.naver.com') ||
                  request.url.toString().contains('search.yahoo.co.jp')) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Search failed. Please check network connection.'),
                    duration: Duration(seconds: 3),
                  )
                );
              } else {
                // 只显示重要的错误
                if (!errorDescription.contains('frame load interrupted')) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Page failed to load: ${error.description}'),
                      duration: const Duration(seconds: 3),
                    )
                  );
                }
              }
            }
          },
          onUpdateVisitedHistory: (controller, url, isReload) {
            if (url != null && mounted) {
              final urlString = url.toString();
              // debugPrint('📚 onUpdateVisitedHistory: $urlString (isReload: $isReload)');

              final updatedTab = widget.tab.copyWith(url: urlString);
              widget.onTabUpdated(updatedTab);

              // History recording moved to onTitleChanged to ensure we have proper titles
            }
          },
          shouldOverrideUrlLoading: (controller, navigationAction) async {
            final url = navigationAction.request.url?.toString();
            final isMainFrame = navigationAction.isForMainFrame;
            // final navigationType = navigationAction.navigationType;
            
            // debugPrint('🔀 shouldOverrideUrlLoading:');
            // debugPrint('   URL: $url');
            // debugPrint('   isMainFrame: $isMainFrame');
            // debugPrint('   navigationType: $navigationType');
            // debugPrint('   当前重定向计数: $_redirectCount');
            
            // 检测无限重定向
            if (url != null && isMainFrame) {
              if (_lastUrl == url) {
                _redirectCount++;
                // debugPrint('🔄 重复URL检测到，重定向计数增加到: $_redirectCount');
                if (_redirectCount > _maxRedirects) {
                  debugPrint('🚫 检测到无限重定向，停止加载: $url');
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Too many redirects, loading has been stopped'),
                        duration: Duration(seconds: 3),
                      )
                    );
                  }
                  return NavigationActionPolicy.CANCEL;
                }
              } else {
                _redirectCount = 0;
                _lastUrl = url;
                // debugPrint('🆕 新URL，重置重定向计数器: $url');
              }
            }
            
            if (_isAdBlockingEnabled) {
              final adBlockResult = await _shouldBlockAd(navigationAction);
              if (adBlockResult == NavigationActionPolicy.CANCEL) {
                debugPrint('🚫 广告拦截器阻止了请求: $url');
                return NavigationActionPolicy.CANCEL;
              }
            }
            
            if (isMainFrame && navigationAction.request.url != null && mounted) {
              final updatedTab = widget.tab.copyWith(url: navigationAction.request.url.toString());
              widget.onTabUpdated(updatedTab);
            }
            
            // debugPrint('✅ 允许导航到: $url');
            return NavigationActionPolicy.ALLOW;
          },
        ),
        if (_showRecommended) RecommendedSitesView(onTap: (url) => loadUrl(url)),
      ],
    );
  }

  // 保持页面状态，避免切换 Tab 时 WebView 被 dispose 导致 controller 失效
  @override
  bool get wantKeepAlive => true;
} 