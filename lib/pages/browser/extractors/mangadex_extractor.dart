import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:http/http.dart' as http;
import 'base_extractor.dart';

/// MangaDex网站图片提取器
/// 专门处理MangaDex网站的图片提取和下载
class MangaDexExtractor extends BaseImageExtractor {
  MangaDexExtractor({
    required InAppWebViewController controller,
    required String currentUrl,
  }) : super(
          controller: controller,
          currentUrl: currentUrl,
        );

  @override
  Future<List<String>> extractImageUrls() async {
    try {
      // 提取 chapterId（URL 最后的 UUID）
      final uri = Uri.parse(currentUrl);
      final segments = uri.pathSegments;
      // 查找chapter后面的UUID段
      // URL形如: https://mangadx.org/chapter/675ddbad-c187-447a-a77d-340b069b1bdc 或
      //         https://mangadx.org/chapter/675ddbad-c187-447a-a77d-340b069b1bdc/3
      String? chapterId;
      final chapterIndex = segments.indexOf('chapter');
      if (chapterIndex != -1 && chapterIndex + 1 < segments.length) {
        chapterId = segments[chapterIndex + 1]; // 取chapter后面的第一段，即UUID
      }

      if (chapterId == null || chapterId.isEmpty) {
        debugPrint("MangaDex: 无法从 URL 中提取 chapterId");
        debugPrint("URL段落: $segments");
        return [];
      }

      debugPrint("从URL提取到章节ID: $chapterId (URL: $currentUrl)");
      debugPrint("https://api.mangadex.org/at-home/server/$chapterId");
      // 请求 MangaDex API 获取图片信息
      final response = await http.get(
        Uri.parse("https://api.mangadex.org/at-home/server/$chapterId"),
      );

      if (response.statusCode != 200) {
        debugPrint("MangaDex API 请求失败，状态码: ${response.statusCode}");
        return [];
      }

      // 解析返回的 JSON 数据
      final Map<String, dynamic> data = json.decode(response.body);
      final baseUrl = data['baseUrl'];
      final hash = data['chapter']['hash'];
      final pageData = List<String>.from(data['chapter']['data']);

      // 构建图片的完整 URL
      final List<String> imageUrls = pageData.map((filename) {
        return '$baseUrl/data/$hash/$filename';
      }).toList();

      debugPrint("从MangaDex API提取到 ${imageUrls.length} 个图片URL");

      // 过滤背景图片和装饰性图片
      final List<String> filteredUrls = await _filterBackgroundImages(imageUrls);

      debugPrint("过滤后剩余 ${filteredUrls.length} 个内容图片");
      for (int i = 0; i < filteredUrls.length; i++) {
        debugPrint("MangaDex内容图片[$i]: ${filteredUrls[i]}");
      }

      return filteredUrls;
    } catch (e) {
      debugPrint("MangaDex图片提取失败: $e");
      return [];
    }
  }

  @override
  bool needsWebViewForImages() {
    // MangaDx 使用 blob URL，必须通过 WebView 获取图片
    return true;
  }

  /// 过滤背景图片和装饰性图片
  /// 通过检查页面中的图片元素来判断哪些是背景图片
  Future<List<String>> _filterBackgroundImages(List<String> imageUrls) async {
    try {
      // JavaScript代码来检查页面中的图片元素
      const String jsCheckImages = '''
        function checkImageElements() {
          const results = {};
          const images = document.querySelectorAll('img');

          images.forEach(img => {
            const src = img.src;
            const computedStyle = window.getComputedStyle(img);
            const parent = img.parentElement;
            const parentStyle = parent ? window.getComputedStyle(parent) : null;

            // 检查是否是背景图片的特征
            const isBackground = (
              // 检查CSS类名
              img.className.includes('background') ||
              img.className.includes('bg-') ||
              img.className.includes('backdrop') ||
              img.className.includes('wallpaper') ||

              // 检查父元素类名
              (parent && (
                parent.className.includes('background') ||
                parent.className.includes('bg-') ||
                parent.className.includes('backdrop') ||
                parent.className.includes('wallpaper')
              )) ||

              // 检查CSS样式
              computedStyle.position === 'fixed' ||
              computedStyle.zIndex < 0 ||
              (parentStyle && parentStyle.position === 'fixed') ||

              // 检查图片尺寸 - 背景图片通常很大或很小
              (img.naturalWidth > 0 && img.naturalHeight > 0 && (
                (img.naturalWidth > 2000 || img.naturalHeight > 2000) || // 很大的背景图
                (img.naturalWidth < 100 && img.naturalHeight < 100)      // 很小的装饰图
              )) ||

              // 检查显示尺寸与自然尺寸的比例 - 背景图片通常被大幅缩放
              (img.naturalWidth > 0 && img.naturalHeight > 0 &&
               img.offsetWidth > 0 && img.offsetHeight > 0 && (
                 (img.offsetWidth / img.naturalWidth < 0.3) ||
                 (img.offsetHeight / img.naturalHeight < 0.3) ||
                 (img.offsetWidth / img.naturalWidth > 3) ||
                 (img.offsetHeight / img.naturalHeight > 3)
               ))
            );

            results[src] = {
              isBackground: isBackground,
              className: img.className,
              parentClassName: parent ? parent.className : '',
              position: computedStyle.position,
              zIndex: computedStyle.zIndex,
              naturalSize: { width: img.naturalWidth, height: img.naturalHeight },
              displaySize: { width: img.offsetWidth, height: img.offsetHeight }
            };
          });

          return JSON.stringify(results);
        }
        checkImageElements();
      ''';

      final result = await controller.evaluateJavascript(source: jsCheckImages);
      if (result == null) {
        debugPrint("MangaDx: 无法获取图片元素信息，返回所有图片");
        return imageUrls;
      }

      // 解析结果
      final Map<String, dynamic> imageInfo = json.decode(result.toString());
      final List<String> filteredUrls = [];

      for (final imageUrl in imageUrls) {
        bool shouldInclude = true;

        // 检查是否有对应的页面元素信息
        for (final entry in imageInfo.entries) {
          final pageSrc = entry.key;
          final info = entry.value as Map<String, dynamic>;

          // 如果URL匹配或包含关系
          if (pageSrc.contains(imageUrl.split('/').last) ||
              imageUrl.contains(pageSrc.split('/').last)) {

            if (info['isBackground'] == true) {
              debugPrint("MangaDx: 过滤背景图片: $imageUrl");
              debugPrint("  - 类名: ${info['className']}");
              debugPrint("  - 父类名: ${info['parentClassName']}");
              debugPrint("  - 位置: ${info['position']}");
              debugPrint("  - 自然尺寸: ${info['naturalSize']}");
              debugPrint("  - 显示尺寸: ${info['displaySize']}");
              shouldInclude = false;
              break;
            }
          }
        }

        if (shouldInclude) {
          filteredUrls.add(imageUrl);
        }
      }

      return filteredUrls;
    } catch (e) {
      debugPrint("MangaDx: 背景图片过滤失败: $e");
      // 如果过滤失败，返回所有图片
      return imageUrls;
    }
  }
}
