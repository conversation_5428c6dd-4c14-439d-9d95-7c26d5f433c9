import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'base_extractor.dart'; 

class MangaHereExtractor extends BaseImageExtractor {
  MangaHereExtractor({
    required InAppWebViewController controller,
    required String currentUrl,
  }) : super(controller: controller, currentUrl: currentUrl);

  @override
  Future<List<String>> extractImageUrls() async {
    final String script = '''
      (function() {
        const imgElement = document.querySelector("#viewer img");
        if (imgElement) {
          // 检查显示尺寸，过滤掉太小的图片
          const displayWidth = imgElement.width || imgElement.offsetWidth || 0;
          const displayHeight = imgElement.height || imgElement.offsetHeight || 0;

          if (displayWidth >= 200 && displayHeight >= 200) {
            return imgElement.src;
          } else {
            console.log('MangaHere: 图片太小，跳过:', displayWidth + 'x' + displayHeight);
            return null;
          }
        }
        return null;
      })();
    ''';

    try {
      final result = await controller.evaluateJavascript(source: script);
      if (result is String && result != 'null') {
        debugPrint("提取成功");
        return [result];
      }
      debugPrint("提取失败");
      return [];
    } catch (e) {
      debugPrint("提取失败: $e");
      return [];
    }
  }
}
