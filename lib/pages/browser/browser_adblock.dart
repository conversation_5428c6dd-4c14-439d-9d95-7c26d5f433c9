// import 'package:flutter/material.dart';
// import 'package:imtrans/util/theme_manager.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:imtrans/services/event_log.dart'; 
// import 'package:imtrans/l10n/generated/app_localizations.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'download.dart';
// import 'extractors/extractor_factory.dart';
// import 'package:imtrans/services/ad_block_service.dart';

// @deprecated
// class BrowserAdBlockPage extends StatefulWidget {
//   final String? initialSiteName;
//   final String? initialUrl;
  
//   const BrowserAdBlockPage({
//     Key? key, 
//     this.initialSiteName,
//     this.initialUrl,  
//   }) : super(key: key);

//   @override
//   State<BrowserAdBlockPage> createState() => _BrowserAdBlockPageState();
// }

// class _BrowserAdBlockPageState extends State<BrowserAdBlockPage> {
//   InAppWebViewController? _webViewController;
//   final UniqueKey _webViewKey = UniqueKey();
//   bool _isLoading = true;
//   bool _isDownloading = false; 
//   String _currentUrl = '';
//   final TextEditingController _urlController = TextEditingController();
//   final AdBlockService _adBlockService = AdBlockService();
//   bool _isAdBlockingEnabled = true;
//   String? _pendingInitialInput;

//   // AdBlockerController? _adBlockerController;
  
//   // 替换 InAppWebViewGroupOptions 为 InAppWebViewSettings
//   InAppWebViewSettings options = InAppWebViewSettings(
//     // isInspectable: false,
//     useShouldOverrideUrlLoading: true,
//     mediaPlaybackRequiresUserGesture: false,
//     javaScriptEnabled: true,
//     // Android 特定设置现在集成到主设置中
//     // androidShouldInterceptRequest: true,
//     // iOS 特定设置现在集成到主设置中
//     allowsInlineMediaPlayback: true,
//   );
  
//   @override
//   void initState() {
//     super.initState();

//     // 关闭调试日志
//     PlatformInAppWebViewController.debugLoggingSettings.enabled = false;

//     // 初始化广告拦截服务
//     _adBlockService.initialize();
    
//     // 加载广告拦截设置
//     _loadAdBlockingSettings();
    
//     // 如果有初始URL，保存待加载输入并更新地址栏
//     if (widget.initialUrl != null && widget.initialUrl!.isNotEmpty) {
//       _pendingInitialInput = widget.initialUrl!.trim();
//       _currentUrl = _pendingInitialInput!;
//       _urlController.text = _pendingInitialInput!;
//     }
//   }
  
//   // 加载广告拦截设置
//   Future<void> _loadAdBlockingSettings() async {
//     final prefs = await SharedPreferences.getInstance();
//     if (mounted) {
//       setState(() {
//         _isAdBlockingEnabled = prefs.getBool('ad_blocking_enabled') ?? true; // 默认启用广告拦截
//       });
//     }
//   }

//   // 添加停止加载方法
//   void _stopLoading() {
//     _webViewController?.stopLoading();
    
//     // 重置加载状态
//     if (mounted) {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   @override
//   void dispose() {
//     // 清理资源
//     _urlController.dispose();
//     super.dispose();
//   }

//   // 获取搜索URL，根据地区选择合适的搜索引擎
//   String _getSearchUrl(String encodedQuery) {
//     // 可以根据用户的语言环境选择不同的搜索引擎
//     final locale = Localizations.localeOf(context);
    
//     switch (locale.languageCode) {
//       case 'zh':
//         // 中文用户使用百度搜索
//         return 'https://www.baidu.com/s?wd=$encodedQuery';
//       case 'ja':
//         // 日文用户使用Yahoo Japan
//         return 'https://search.yahoo.co.jp/search?p=$encodedQuery';
//       case 'ko':
//         // 韩文用户使用Naver
//         return 'https://search.naver.com/search.naver?query=$encodedQuery';
//       default:
//         // 其他语言使用Google搜索
//         return 'https://www.google.com/search?q=$encodedQuery';
//     }
//   }

//   // 判断输入的文本是否为有效的URL
//   bool _isValidUrl(String text) {
//     // 如果包含空格，很可能是搜索词而不是URL
//     if (text.contains(' ')) {
//       return false;
//     }
    
//     // 明确的协议前缀
//     if (text.startsWith('http://') || text.startsWith('https://') || text.startsWith('ftp://')) {
//       return true;
//     }
    
//     // localhost相关
//     if (text.startsWith('localhost') || text == 'localhost') {
//       return true;
//     }
    
//     // 检查是否为IP地址
//     final ipPattern = RegExp(
//       r'^((\d{1,3}\.){3}\d{1,3})(:\d+)?$',
//       caseSensitive: false,
//     );
//     if (ipPattern.hasMatch(text)) {
//       return true;
//     }
    
//     // 检查是否包含明确的域名格式（必须包含点和有效的顶级域名）
//     final domainPattern = RegExp(
//       r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$',
//       caseSensitive: false,
//     );
    
//     // 检查常见的顶级域名
//     final commonTlds = [
//       'com', 'org', 'net', 'edu', 'gov', 'mil', 'int', 'co', 'io', 'me', 'tv', 
//       'info', 'biz', 'name', 'pro', 'museum', 'aero', 'coop', 'jobs', 'travel', 
//       'mobi', 'asia', 'cat', 'tel', 'xxx', 'post', 'geo', 'local', 'cn', 'jp', 
//       'kr', 'uk', 'de', 'fr', 'it', 'es', 'ru', 'br', 'au', 'ca'
//     ];
    
//     if (domainPattern.hasMatch(text)) {
//       // 进一步检查是否包含有效的顶级域名
//       final parts = text.split('.');
//       if (parts.length >= 2) {
//         final tld = parts.last.toLowerCase();
//         return commonTlds.contains(tld);
//       }
//     }
    
//     return false;
//   }

//   Future<void> _loadUrl([String? url]) async {
//     String inputText = url ?? _urlController.text.trim();
    
//     if (inputText.isEmpty) {
//       return;
//     }
    
//     String finalUrl;
    
//     try {
//       if (_isValidUrl(inputText)) {
//         // 如果是有效的URL，添加协议前缀（如果需要）
//         if (!inputText.startsWith('http://') && !inputText.startsWith('https://')) {
//           finalUrl = 'https://$inputText';
//         } else {
//           finalUrl = inputText;
//         }
//         debugPrint('加载URL: $finalUrl');
//       } else {
//         // 如果不是有效的URL，使用搜索引擎搜索
//         final encodedQuery = Uri.encodeComponent(inputText);
//         finalUrl = _getSearchUrl(encodedQuery);
//         debugPrint('搜索关键词: $inputText -> $finalUrl');
//       }
      
//       // 验证最终URL是否有效
//       final uri = Uri.tryParse(finalUrl);
//       if (uri == null || !uri.hasScheme) {
//         debugPrint('无效的URL: $finalUrl');
//         return;
//       }
      
//       await _webViewController?.loadUrl(
//         urlRequest: URLRequest(url: WebUri(finalUrl))
//       );
      
//       // 手动加载URL时记录事件
//       EventLogService.logWebsiteVisit(
//         url: finalUrl, 
//         method: _isValidUrl(inputText) ? 'manual_input' : 'search'
//       );
//     } catch (e) {
//       debugPrint('加载URL时出错: $e');
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text('Load failed: ${e.toString()}'))
//         );
//       }
//     }
//   }
  
//   /// 点击"下载"按钮后
//   Future<void> _startDownload(BuildContext context) async {
//     setState(() {
//       _isDownloading = true;
//     });
//     try {
//       // 创建图片提取器实例
//       final extractor = ExtractorFactory.createExtractor(
//         controller: _webViewController!,
//         currentUrl: _currentUrl,
//       );
      
//       // 提取图片URL
//       final urls = await extractor.extractImageUrls();
//       debugPrint("Browser提取到 ${urls.length} 个图片URL：$urls");
      
//       if (urls.isEmpty) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(AppLocalizations.of(context)!.noImagesFound))
//         );
//       } 
//       else {
//         // 检查是否需要使用WebView获取图片
//         final needsWebView = extractor.needsWebViewForImages();
        
//         if (needsWebView) {
//           debugPrint("检测到需要使用WebView获取图片的网站: $_currentUrl");
          
//           // 从WebView获取图片数据
//           final images = await extractor.downloadImages(urls);
//           debugPrint("WebView获取到 ${images.length} 张有效图片");
          
//           if (images.isNotEmpty) {
//             // 使用获取到的图片数据创建新的下载页面
//             Navigator.push(
//               context,
//               MaterialPageRoute(builder: (_) {
//                 return BrowserDownloadPage(
//                   imageUrls: urls.sublist(0, images.length), // 确保URL和图片数量一致
//                   preloadedImages: images,
//                 );
//               }),
//             );
//           } else {
//             ScaffoldMessenger.of(context).showSnackBar(
//               SnackBar(content: Text(AppLocalizations.of(context)!.unableToProcessImages))
//             );
//           }
//         } else {
//           // 对于普通网站，直接跳转到下载页面
//           debugPrint("检测到不需要使用WebView获取图片的网站: $_currentUrl");
//           final String cookies = await extractor.getCookies();
//           Navigator.push(
//             context,
//             MaterialPageRoute(builder: (_) {
//               return BrowserDownloadPage(
//                 imageUrls: urls,
//                 cookies: cookies,
//                 referer: _currentUrl,
//                 );
//             }),
//           );
//         }
//       }
//     } catch (e) {
//       debugPrint("Download Error: $e");
//       if (mounted) { 
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text(AppLocalizations.of(context)!.downloadFailed(e.toString())))
//         );
//       }
//     } finally {
//       if (mounted) { 
//         setState(() {
//           _isDownloading = false; // 重置下载状态
//         });
//       }
//     }
//   }

//   // 切换广告拦截状态
//   // void _toggleAdBlocking() {
//   //   setState(() {
//   //     _isAdBlockingEnabled = !_isAdBlockingEnabled;
//   //   });
    
//   //   if (_isAdBlockingEnabled) {
//   //     _adBlockerController?.enable();
//   //     ScaffoldMessenger.of(context).showSnackBar(
//   //       SnackBar(content: Text(AppLocalizations.of(context)!.adBlockingEnabled))
//   //     );
//   //   } else {
//   //     _adBlockerController?.disable();
//   //     ScaffoldMessenger.of(context).showSnackBar(
//   //       SnackBar(content: Text(AppLocalizations.of(context)!.adBlockingDisabled))
//   //     );
//   //   }
//   // }

//   bool _isSearchEngineDomain(String host) {
//     const searchDomains = {
//       'www.google.com',
//       'www.gstatic.com',
//       'gstatic.com',
//       'www.bing.com',
//       'www.baidu.com',
//       'm.baidu.com',
//       'img.baidu.com',
//       'www.baiduimg.cn',
//       'search.yahoo.co.jp',
//       'yimg.jp',
//       'search.naver.com',
//       'ssl.pstatic.net',
//     };

//     // 如果完全匹配或以搜索域结尾则视为搜索引擎资源
//     return searchDomains.any((d) => host == d || host.endsWith('.' + d.replaceFirst('www.', '')));
//   }

//   Future<NavigationActionPolicy> _shouldBlockAd(NavigationAction navigationAction) async {
//     var uri = navigationAction.request.url!;
//     final String urlString = uri.toString().toLowerCase();
//     final String host = uri.host.toLowerCase();
//     final String path = uri.path.toLowerCase();

//     // 如果属于搜索引擎域名，直接放行所有资源，避免白屏
//     if (_isSearchEngineDomain(host)) {
//       return NavigationActionPolicy.ALLOW;
//     }

//     // 使用AdBlockService检查URL
//     if (_adBlockService.shouldBlockUrl(urlString, host, path)) {
//       return NavigationActionPolicy.CANCEL;
//     }

//     // 如果不是广告，更新当前URL（主页面导航时）
//     if (navigationAction.isForMainFrame && mounted) {
//       setState(() {
//         _currentUrl = urlString;
//         _urlController.text = urlString;
//       });
//     }
//     return NavigationActionPolicy.ALLOW;
//   }

//   @override
//   Widget build(BuildContext context) {
//     final themeColors = ThemeManager.currentTheme;
    
//     return Scaffold(
//       backgroundColor: themeColors.backgroundColor,
//       body: SafeArea(
//         child: Column(
//           children: [
//             // Appbar
//             Container(
//               padding: const EdgeInsets.only(left:10, right: 20, bottom: 4),
//               color: themeColors.backgroundColor, 
//               child: Row(
//                 children: [
//                   IconButton(
//                     icon: Image.asset(ThemeManager.getImagePath('btn_close'), width: 40, height: 40),
//                     onPressed: () {
//                       // 返回时将当前URL作为结果传递回上一页
//                       Navigator.of(context).pop(_currentUrl);
//                     },
//                   ),
//                   Expanded(
//                     child: Container(
//                       height: 40,
//                       padding: const EdgeInsets.symmetric(horizontal: 14),
//                       decoration: BoxDecoration(
//                         color: themeColors.inputBoxBgColor,
//                         borderRadius: BorderRadius.circular(18),
//                         border: Border.all(color: themeColors.textColor, width: 1), 
//                       ),
//                       child: Row(
//                         children: [
//                           // 加载指示器放在输入框前面
//                           if (_isLoading)
//                             Container(
//                               width: 16,
//                               height: 16,
//                               margin: const EdgeInsets.only(right: 8),
//                               child: CircularProgressIndicator(
//                                 strokeWidth: 2,
//                                 color: themeColors.textColor, 
//                               ),
//                             ),
//                           Expanded(
//                             child: TextField(
//                               controller: _urlController,
//                               decoration: InputDecoration(
//                                 border: InputBorder.none,
//                                 hintText: AppLocalizations.of(context)!.enterUrl,
//                                 hintStyle: TextStyle(fontSize: 13, color: Colors.grey.shade400),
//                                 contentPadding: const EdgeInsets.symmetric(vertical: 10),
//                                 isDense: true,
//                               ),
//                               style: TextStyle(fontSize: 14, color: themeColors.textColor), 
//                               onEditingComplete: _loadUrl,
//                               textAlignVertical: TextAlignVertical.center,
//                             ),
//                           ),
//                           // 放大镜图标在加载时变成X，点击可以停止加载
//                           Padding(
//                             padding: const EdgeInsets.only(left: 4.0),
//                             child: GestureDetector(
//                               onTap: () {
//                                 if (_isLoading) {
//                                    // 停止加载
//                                   _stopLoading();
//                                 } else {
//                                   // 不在加载时，点击执行搜索
//                                   _loadUrl();
//                                 }
//                               },
//                               child: _isLoading 
//                                 ? Icon(Icons.close, size: 24, color: themeColors.textColor)
//                                 : Image.asset(
//                                     ThemeManager.getImagePath('icon_search'),
//                                     width: 24,
//                                     height: 24,
//                                     color: themeColors.textColor,
//                                   ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
            
//             // WebView 内容
//             Expanded(
//               child: Stack(
//                 children: [
//                   InAppWebView(
//                     key: _webViewKey,
//                     initialSettings: options,
//                     initialUrlRequest: null, // 不直接加载初始URL，而是通过_loadUrl方法处理
//                     onWebViewCreated: (controller) {
//                       _webViewController = controller;
//                       // WebView 创建完成后加载待处理的初始输入
//                       if (_pendingInitialInput != null) {
//                         _loadUrl(_pendingInitialInput);
//                         _pendingInitialInput = null;
//                       }
//                     },
//                     onLoadStart: (controller, url) {
//                       if (url != null && mounted) {
//                         setState(() {
//                           _isLoading = true;
//                           _currentUrl = url.toString();
//                           _urlController.text = url.toString();
//                         });
//                       }
//                     },
//                     onLoadStop: (controller, url) {
//                       if (url != null && mounted) {
//                         setState(() {
//                           _isLoading = false;
//                           _currentUrl = url.toString();
//                           _urlController.text = url.toString();
//                         });
//                       }
//                     },
//                     onReceivedError: (controller, request, error) {
//                       debugPrint('WebView error: ${error.toString()}');
//                       debugPrint('Failed URL: ${request.url}');
                      
//                       if (mounted) {
//                         setState(() {
//                           _isLoading = false;
//                         });
                        
//                         // 如果是搜索URL出错，可能是网络问题，显示友好的错误信息
//                         if (request.url.toString().contains('search?q=') || 
//                             request.url.toString().contains('s?wd=') ||
//                             request.url.toString().contains('search.naver.com') ||
//                             request.url.toString().contains('search.yahoo.co.jp')) {
//                           ScaffoldMessenger.of(context).showSnackBar(
//                             const SnackBar(
//                               content: Text('Search failed. Please check network connection.'),
//                               duration: Duration(seconds: 3),
//                             )
//                           );
//                         } else {
//                           ScaffoldMessenger.of(context).showSnackBar(
//                             SnackBar(
//                               content: Text('Page failed to load: ${error.toString()}'),
//                               duration: const Duration(seconds: 3),
//                             )
//                           );
//                         }
//                       }
//                     },
//                     onProgressChanged: (controller, progress) {
//                       // 可以添加进度条显示
//                     },
//                     // 添加URL历史更新回调
//                     onUpdateVisitedHistory: (controller, url, isReload) {
//                       if (url != null && mounted) {
//                         setState(() {
//                           _currentUrl = url.toString();
//                           _urlController.text = url.toString();
//                         });
//                       }
//                     },
//                     // 添加广告拦截功能
//                     shouldOverrideUrlLoading: (controller, navigationAction) async {
//                       // 只处理广告拦截，不处理地址栏更新
//                       if (_isAdBlockingEnabled) {
//                         return _shouldBlockAd(navigationAction);
//                       }
                      
//                       // 在非广告拦截模式下也更新URL（主页面导航时）
//                       if (navigationAction.isForMainFrame && navigationAction.request.url != null && mounted) {
//                         setState(() {
//                           _currentUrl = navigationAction.request.url.toString();
//                           _urlController.text = navigationAction.request.url.toString();
//                         });
//                       }
                      
//                       return NavigationActionPolicy.ALLOW;
//                     },
//                   ),
//                 ],
//               ),
//             ),
            
//             // 底部工具栏
//             Container(
//               padding: const EdgeInsets.only(top: 8),
//               decoration: BoxDecoration(
//                 color: themeColors.backgroundColor, 
//               ),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                 children: [
//                   IconButton(
//                     icon: Icon(Icons.arrow_back_ios, color: themeColors.textColor, size: 20), 
//                     onPressed: () async {
//                       if (await _webViewController?.canGoBack() ?? false) {
//                         _webViewController?.goBack();
//                       }
//                     },
//                   ),
//                   IconButton(
//                     icon: Icon(Icons.arrow_forward_ios, color: themeColors.textColor, size: 20), 
//                     onPressed: () async {
//                       if (await _webViewController?.canGoForward() ?? false) {
//                         _webViewController?.goForward();
//                       }
//                     },
//                   ),
//                   IconButton(
//                     icon: Icon(Icons.refresh, color: themeColors.textColor, size: 28), 
//                     onPressed: () {
//                       _webViewController?.reload();
//                     },
//                   ),
//                   Container(
//                     width: 160,
//                     height: 38,
//                     decoration: BoxDecoration(
//                       color: const Color(0xFFCDEE2D), 
//                       borderRadius: BorderRadius.circular(20),
//                       shape: BoxShape.rectangle,
//                       border: Border.all(color: Colors.black87, width: 1),
//                     ),
//                     child: _isDownloading
//                       ? const Center(
//                           child: SizedBox(
//                             width: 22,
//                             height: 22,
//                             child: CircularProgressIndicator(
//                               strokeWidth: 2,
//                               color: Colors.black87,
//                             ),
//                           ),
//                         )
//                       : Material(
//                           color: Colors.transparent,
//                           child: InkWell(
//                             onTap: () => _startDownload(context),
//                             borderRadius: BorderRadius.circular(20),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 const Icon(Icons.translate, color: Colors.black87,size: 20,),
//                                 const SizedBox(width: 8),
//                                 Text(
//                                   AppLocalizations.of(context)!.translate,
//                                   style: const TextStyle(
//                                     color: Colors.black87,
//                                     fontWeight: FontWeight.bold,
//                                     fontSize: 13,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
