import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/models/browser_tab.dart';
import 'package:imtrans/pages/browser/browser_tab_view.dart';
import 'package:imtrans/pages/browser/download.dart';
import 'package:imtrans/pages/browser/extractors/extractor_factory.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/widgets/common/common_widgets.dart';
import 'package:imtrans/widgets/toast_widget.dart';
import 'package:imtrans/services/bookmark_service.dart';
import 'package:imtrans/services/translation_workflow_service.dart';
import 'package:imtrans/services/download_notification_service.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:imtrans/services/webview_js_resources.dart';
import 'package:imtrans/services/webview_injection_service.dart';

class MultiBrowserPage extends StatefulWidget {
  // final String? initialSiteName;
  final String? initialUrl;

  const MultiBrowserPage({
    Key? key,
    // this.initialSiteName,
    this.initialUrl,
  }) : super(key: key);

  @override
  State<MultiBrowserPage> createState() => _MultiBrowserPageState();
}

class _MultiBrowserPageState extends State<MultiBrowserPage> with TickerProviderStateMixin {
  List<BrowserTab> _tabs = [];
  int _currentTabIndex = 0;
  final TextEditingController _urlController = TextEditingController();
  final FocusNode _urlFocusNode = FocusNode();
  bool _isDownloading = false;
  bool _isLocalTranslating = false;
  late TabController _tabController;
  final Map<String, GlobalKey<BrowserTabViewState>> _tabKeys = {};

  // Translation workflow service
  final TranslationWorkflowService _translationWorkflow = TranslationWorkflowService();
  bool _servicesInitialized = false;

  // WebView 注入服务
  final WebViewInjectionService _injectionService = WebViewInjectionService();

  // Translation toggle state
  bool _translationToggleEnabled = false;

  // 防重复处理机制
  final Set<String> _processingImages = <String>{}; // 正在处理的图片URL集合
  final Map<String, DateTime> _lastProcessTime = <String, DateTime>{}; // 图片最后处理时间
  static const Duration _minProcessInterval = Duration(milliseconds: 1000); // 最小处理间隔

  // More menu state
  bool _isMoreMenuVisible = false;

  // 用于管理每个地址栏的状态
  final Map<String, TextEditingController> _addressControllers = {};
  final Map<String, FocusNode> _addressFocusNodes = {};
  String? _editingTabId; // 当前正在编辑的tab ID

  // 用于跟踪URL变化和动作按钮状态
  final Map<String, String> _lastKnownUrls = {}; // 跟踪每个tab的最后已知URL
  final Map<String, DateTime> _urlChangeTimestamps = {}; // 跟踪URL变化时间
  final ScrollController _tabScrollController = ScrollController(); // 控制tab滚动
  bool _canGoForwardState = false; // 跟踪前进按钮状态
  bool _canGoBackState = false; // 跟踪后退按钮状态

  @override
  void initState() {
    super.initState();
    _initializeBrowser();
    _initializeServices();
  }

  // 初始化浏览器状态
  Future<void> _initializeBrowser() async {
    // 创建初始tab - 总是从干净状态开始
    final initialTab = BrowserTab(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
    );
    _tabs.add(initialTab);
    _tabKeys[initialTab.id] = GlobalKey<BrowserTabViewState>();
    _createAddressBarControllers(initialTab.id);

    // 初始化TabController
    _tabController = TabController(length: _tabs.length, vsync: this, initialIndex: _currentTabIndex);
    _tabController.addListener(_onTabChanged);

    // 智能处理初始URL
    if (widget.initialUrl != null && widget.initialUrl!.isNotEmpty) {
      _handleInitialUrl(widget.initialUrl!);
    } else {
      // 更新UI - 显示推荐网站页面
      setState(() {
        // 更新地址栏
        if (_currentTabIndex < _tabs.length) {
          _urlController.text = _tabs[_currentTabIndex].url;
        }
      });

      // 初始化后居中显示当前tab
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _centerCurrentTab();
      });
    }
  }

  // 智能处理初始URL
  void _handleInitialUrl(String initialUrl) {
    // 检查是否有空白tab可以重用
    bool hasEmptyTab = _tabs.any((tab) => tab.isEmpty);
    bool hasContentTab = _tabs.any((tab) => tab.hasContent);

    if (!hasContentTab || hasEmptyTab) {
      // 如果没有内容tab或有空白tab，重用第一个tab
      debugPrint('重用现有空白tab加载URL: $initialUrl');
      _loadUrlToTab(_tabs.first.id, initialUrl);
    } else {
      // 如果所有tab都有内容，创建新tab
      debugPrint('创建新tab加载URL: $initialUrl');
      _createNewTab(initialInput: initialUrl);
    }
  }

  // 加载URL到指定tab
  void _loadUrlToTab(String tabId, String url) {
    final tabIndex = _tabs.indexWhere((tab) => tab.id == tabId);
    if (tabIndex == -1) return;

    setState(() {
      _currentTabIndex = tabIndex;
      _tabController.animateTo(tabIndex);
      _urlController.text = url;

      // 更新地址栏控制器
      _addressControllers[tabId]?.text = url;
    });

    // 居中显示tab
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _centerCurrentTab();
    });

    // 延迟加载URL以确保WebView准备好
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        final tabViewState = _tabKeys[tabId]?.currentState;
        if (tabViewState != null) {
          tabViewState.loadUrl(url);
        }
      }
    });
  }

  @override
  void dispose() {
    // 首先关闭翻译功能，确保ML Kit资源被正确清理
    if (_translationToggleEnabled) {
      debugPrint('MultiBrowserPage: Disabling translation before dispose');
      _disableTranslation(skipUI: true).catchError((e) {
        debugPrint('Error disabling translation during dispose: $e');
      });
    }

    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _urlController.dispose();
    _urlFocusNode.dispose();
    _tabScrollController.dispose();

    // 清理所有地址栏控制器
    for (String tabId in _addressControllers.keys.toList()) {
      _disposeAddressBarControllers(tabId);
    }

    // 清理翻译工作流服务 - 异步调用但不等待，避免阻塞dispose
    _translationWorkflow.dispose().catchError((e) {
      debugPrint('Error disposing translation workflow: $e');
    });

    // 清理下载通知服务
    DownloadNotificationService.instance.dispose();

    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.index != _currentTabIndex && _tabController.index < _tabs.length) {
      final newTabIndex = _tabController.index;
      final newTab = _tabs[newTabIndex];

      // Clean up translation state when switching tabs
      _cleanupTranslationState();

      setState(() {
        _currentTabIndex = newTabIndex;
        _urlController.text = newTab.url;
      });

      // 居中显示新的当前tab
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _centerCurrentTab();
      });

      // 更新前进和后退按钮状态
      _updateForwardButtonState();
      _updateBackButtonState();

      // 强制更新UI以反映新tab的翻译按钮状态
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        // 确保当前tab的WebView被创建
        await _ensureCurrentTabWebViewCreated();
        _refreshTranslateButtonState();
      });
    }
  }

  // 为tab创建地址栏控制器和焦点节点
  void _createAddressBarControllers(String tabId) {
    _addressControllers[tabId] = TextEditingController();
    _addressFocusNodes[tabId] = FocusNode();

    // 初始化URL跟踪
    _lastKnownUrls[tabId] = '';
    _urlChangeTimestamps[tabId] = DateTime.now();

    // 监听焦点变化
    _addressFocusNodes[tabId]!.addListener(() {
      if (_addressFocusNodes[tabId]!.hasFocus) {
        // 只在编辑状态真正改变时才调用setState
        if (_editingTabId != tabId) {
          setState(() {
            _editingTabId = tabId;
          });
          
          // 延迟更新文本内容，让UI先完成textAlign的变化
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final tab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
            if (tab.id.isNotEmpty && _addressControllers[tabId] != null) {
              _addressControllers[tabId]!.text = tab.url;
              _addressControllers[tabId]!.selection = TextSelection(
                baseOffset: 0,
                extentOffset: tab.url.length,
              );
            }
          });
        }
      } else {
        // 失去焦点时，恢复显示域名
        if (_editingTabId == tabId) {
          setState(() {
            _editingTabId = null;
          });
        }
      }
    });
  }

  // 清理tab的地址栏控制器
  void _disposeAddressBarControllers(String tabId) {
    _addressControllers[tabId]?.dispose();
    _addressFocusNodes[tabId]?.dispose();
    _addressControllers.remove(tabId);
    _addressFocusNodes.remove(tabId);
    _lastKnownUrls.remove(tabId);
    _urlChangeTimestamps.remove(tabId);

    if (_editingTabId == tabId) {
      _editingTabId = null;
    }
  }

  // 跟踪URL变化
  void _trackUrlChange(String tabId, String newUrl) {
    final oldUrl = _lastKnownUrls[tabId];
    if (oldUrl != newUrl) {
      _lastKnownUrls[tabId] = newUrl;
      _urlChangeTimestamps[tabId] = DateTime.now();
    }
  }

  // 判断是否应该显示搜索图标（而不是刷新图标）
  bool _shouldShowSearchIcon(String tabId) {
    final tab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
    if (tab.id.isEmpty) return true;

    // 如果正在加载，显示停止图标
    if (tab.isLoading) return false;

    // 如果URL为空或是新tab，显示搜索图标
    if (tab.url.isEmpty) return true;

    // 如果URL最近发生了变化（5秒内），显示搜索图标
    final lastChange = _urlChangeTimestamps[tabId];
    if (lastChange != null) {
      final timeSinceChange = DateTime.now().difference(lastChange);
      if (timeSinceChange.inSeconds < 5) {
        return true;
      }
    }

    // 否则显示刷新图标
    return false;
  }

  // 更新tab信息
  void _updateTab(BrowserTab updatedTab) {
    setState(() {
      final index = _tabs.indexWhere((tab) => tab.id == updatedTab.id);
      if (index != -1) {
        final oldTab = _tabs[index];

        // 跟踪URL变化
        _trackUrlChange(updatedTab.id, updatedTab.url);

        _tabs[index] = updatedTab;

        // 如果是当前tab且加载状态发生变化，触发UI更新
        // if (index == _currentTabIndex && oldTab.isLoading != updatedTab.isLoading) {
        // debugPrint('当前tab加载状态变化: ${updatedTab.isLoading ? "加载中" : "加载完成"}');
        // setState已经在外层调用，这里只需要记录日志
        // }

        // 如果是当前tab，更新地址栏和按钮状态
        if (index == _currentTabIndex) {
          _urlController.text = updatedTab.url;
          // 更新前进和后退按钮状态
          _updateForwardButtonState();
          _updateBackButtonState();

          // 如果controller状态发生变化，需要更新翻译按钮状态
          if (oldTab.controller != updatedTab.controller) {
            debugPrint('当前tab的controller状态发生变化，更新翻译按钮状态');
            // setState已经在外层调用，翻译按钮会自动重新评估
          }
        }

        // 更新对应的地址栏控制器（如果不在编辑状态）
        if (_editingTabId != updatedTab.id && _addressControllers[updatedTab.id] != null) {
          _addressControllers[updatedTab.id]!.text = updatedTab.url;
        }
      }
    });
  }

  // 更新前进按钮状态
  void _updateForwardButtonState() async {
    final canGoForward = await _canGoForward();
    if (mounted && _canGoForwardState != canGoForward) {
      setState(() {
        _canGoForwardState = canGoForward;
      });
    }
  }

  // 更新后退按钮状态
  void _updateBackButtonState() async {
    final canGoBack = await _canGoBack();
    if (mounted && _canGoBackState != canGoBack) {
      setState(() {
        _canGoBackState = canGoBack;
      });
    }
  }

  // 切换到指定tab并居中显示
  void _switchToTab(int index) {
    if (index < 0 || index >= _tabs.length || index == _currentTabIndex) return;

    setState(() {
      _currentTabIndex = index;
      _tabController.animateTo(index);
    });

    // 计算需要滚动的位置以居中显示当前tab
    _centerCurrentTab();

    // 更新前进和后退按钮状态
    _updateForwardButtonState();
    _updateBackButtonState();

    // 强制更新UI以反映新tab的翻译按钮状态
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // 确保当前tab的WebView被创建
      await _ensureCurrentTabWebViewCreated();
      _refreshTranslateButtonState();
    });
  }

  // 将当前tab居中显示
  void _centerCurrentTab() {
    if (!_tabScrollController.hasClients || !mounted) return;

    // 获取屏幕宽度
    final screenWidth = MediaQuery.of(context).size.width;
    // 计算tab宽度（80%屏幕宽度）
    final tabWidth = screenWidth * 0.8;

    // 计算当前tab的左边缘位置，考虑额外的边距
    double currentTabLeftEdge = 0;
    for (int i = 0; i < _currentTabIndex; i++) {
      // 每个tab的宽度
      currentTabLeftEdge += tabWidth;
      // 添加边距：第一个tab左侧40px，最后一个tab右侧40px，其他tab右侧8px
      if (i == 0) {
        currentTabLeftEdge += 40; // 第一个tab的左边距
      }
      if (i == _tabs.length - 1) {
        currentTabLeftEdge += 40; // 最后一个tab的右边距
      } else {
        currentTabLeftEdge += 8; // 普通tab的右边距
      }
    }

    // 如果当前tab是第一个，需要加上它的左边距
    if (_currentTabIndex == 0) {
      currentTabLeftEdge += 40;
    }

    // 计算屏幕中心位置
    final screenCenter = screenWidth / 2;

    // 计算tab中心位置
    final tabCenter = tabWidth / 2;

    // 计算目标滚动位置（使当前tab的中心对齐屏幕中心）
    final targetOffset = currentTabLeftEdge + tabCenter - screenCenter;

    // 确保滚动位置在有效范围内
    final maxOffset = _tabScrollController.position.maxScrollExtent;
    final minOffset = _tabScrollController.position.minScrollExtent;
    final clampedOffset = targetOffset.clamp(minOffset, maxOffset);

    // 平滑滚动到目标位置
    if (_tabScrollController.offset != clampedOffset) {
      _tabScrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // 创建新tab
  void _createNewTab({String? initialInput}) {
    final newTab = BrowserTab(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
    );

    setState(() {
      _tabs.add(newTab);
      _tabKeys[newTab.id] = GlobalKey<BrowserTabViewState>();
      _createAddressBarControllers(newTab.id);
      _currentTabIndex = _tabs.length - 1;

      // 重新创建TabController
      _tabController.removeListener(_onTabChanged);
      _tabController.dispose();
      _tabController = TabController(length: _tabs.length, vsync: this, initialIndex: _currentTabIndex);
      _tabController.addListener(_onTabChanged);

      _urlController.text = initialInput ?? '';
    });

    // 居中显示新tab
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _centerCurrentTab();
    });

    // 如果有初始输入，延迟加载以确保WebView已准备好
    if (initialInput != null && initialInput.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _loadUrl(initialInput);
        }
      });
    }
  }

  // 关闭tab
  void _closeTab(int index) {
    if (index < 0 || index >= _tabs.length) {
      debugPrint('无效的tab索引: $index');
      return;
    }

    if (_tabs.length <= 1) {
      // 如果只有一个tab，关闭整个浏览器
      debugPrint('关闭最后一个tab，返回主页');
      Navigator.of(context).pop();
      return;
    }

    final tabToRemove = _tabs[index];

    // 标记tab为已dispose，防止后续使用
    tabToRemove.markAsDisposed();

    // 不要手动dispose WebView controller
    // InAppWebView会在其Widget dispose时自动释放controller
    // 手动dispose可能导致controller被重复释放或在异步操作中使用已dispose的controller

    setState(() {
      _tabs.removeAt(index);
      _tabKeys.remove(tabToRemove.id);
      _disposeAddressBarControllers(tabToRemove.id);

      // 调整当前tab索引
      if (_currentTabIndex >= _tabs.length) {
        _currentTabIndex = _tabs.length - 1;
      } else if (_currentTabIndex > index) {
        _currentTabIndex--;
      }

      // 确保_currentTabIndex不会为负数
      if (_currentTabIndex < 0) {
        _currentTabIndex = 0;
      }

      // 重新创建TabController
      _tabController.removeListener(_onTabChanged);
      _tabController.dispose();

      if (_tabs.isNotEmpty) {
        _tabController = TabController(length: _tabs.length, vsync: this, initialIndex: _currentTabIndex);
        _tabController.addListener(_onTabChanged);

        // 更新地址栏
        if (_currentTabIndex < _tabs.length) {
          _urlController.text = _tabs[_currentTabIndex].url;
        }
      }
    });
  }

  // 从地址栏加载URL到指定tab
  Future<void> _loadUrlFromAddressBar(String tabId) async {
    final tabIndex = _tabs.indexWhere((tab) => tab.id == tabId);
    if (tabIndex == -1) return;

    final controller = _addressControllers[tabId];
    if (controller == null) return;

    final inputText = controller.text.trim();
    if (inputText.isEmpty) return;

    // 切换到该tab
    setState(() {
      _currentTabIndex = tabIndex;
      _tabController.animateTo(tabIndex);
    });

    // 加载URL
    final tabViewState = _tabKeys[tabId]?.currentState;
    if (tabViewState != null) {
      await tabViewState.loadUrl(inputText);
    }

    // 失去焦点
    _addressFocusNodes[tabId]?.unfocus();
  }

  // 加载URL到当前tab
  Future<void> _loadUrl([String? url]) async {
    if (_currentTabIndex >= _tabs.length) return;

    final currentTab = _tabs[_currentTabIndex];
    final tabViewState = _tabKeys[currentTab.id]?.currentState;

    if (tabViewState != null) {
      final inputText = url ?? _urlController.text.trim();
      if (inputText.isNotEmpty) {
        await tabViewState.loadUrl(inputText);
      }
    } else {
      // 如果TabView还没有准备好，延迟重试
      debugPrint('TabView还没有准备好，延迟重试加载URL');
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && _currentTabIndex < _tabs.length) {
          _loadUrl(url);
        }
      });
    }
  }

  // 刷新指定tab
  void _refreshTab(String tabId) async {
    final tab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
    if (tab.id.isEmpty || tab.isDisposed || !mounted) return;

    debugPrint('刷新tab: ${tab.title} (${tab.url})');

    try {
      // 如果controller不存在，说明WebView还没创建，需要先确保WebView被创建
      if (tab.controller == null) {
        debugPrint('Controller不存在，先确保WebView被创建');

        // 如果不是当前tab，先切换到该tab
        final tabIndex = _tabs.indexWhere((t) => t.id == tabId);
        if (tabIndex != -1 && tabIndex != _currentTabIndex) {
          _switchToTab(tabIndex);
          // 等待tab切换完成
          await Future.delayed(const Duration(milliseconds: 500));
        }

        // 等待WebView controller创建
        await _ensureCurrentTabWebViewCreated();

        // 重新获取最新的tab状态
        final latestTab = _tabs.firstWhere((t) => t.id == tabId, orElse: () => BrowserTab(id: ''));
        if (latestTab.controller != null) {
          await latestTab.controller!.reload();
          debugPrint('WebView创建后刷新成功');
        } else {
          // 如果还是没有controller，使用loadUrl作为备选方案
          debugPrint('Controller仍然不存在，使用loadUrl重新加载');
          if (latestTab.url.isNotEmpty) {
            await latestTab.controller?.loadUrl(urlRequest: URLRequest(url: WebUri(latestTab.url)));
          }
        }
      } else {
        // Controller存在，直接刷新
        await tab.controller!.reload();
        debugPrint('直接刷新成功');
      }

      // 标记URL变化以显示搜索图标
      _trackUrlChange(tabId, tab.url);
    } catch (e) {
      debugPrint('刷新页面时出错: $e');

      // 如果刷新失败，尝试重新加载URL
      try {
        if (tab.url.isNotEmpty && tab.controller != null) {
          await tab.controller!.loadUrl(urlRequest: URLRequest(url: WebUri(tab.url)));
          debugPrint('重新加载URL成功');
        }
      } catch (e2) {
        debugPrint('重新加载URL也失败: $e2');
      }
    }
  }

  // 停止加载
  void _stopLoading() {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          currentTab.controller?.stopLoading();
        } catch (e) {
          debugPrint('停止加载时出错: $e');
        }
      }

      if (mounted) {
        setState(() {
          _tabs[_currentTabIndex] = currentTab.copyWith(isLoading: false);
        });
      }
    }
  }

  // 后退
  Future<void> _goBack() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          if (await currentTab.controller?.canGoBack() ?? false) {
            if (mounted && !currentTab.isDisposed) {
              currentTab.controller?.goBack();
            }
          }
        } catch (e) {
          debugPrint('后退时出错: $e');
        }
      }
    }
  }

  // 前进
  Future<void> _goForward() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          if (await currentTab.controller?.canGoForward() ?? false) {
            if (mounted && !currentTab.isDisposed) {
              currentTab.controller?.goForward();
            }
          }
        } catch (e) {
          debugPrint('前进时出错: $e');
        }
      }
    }
  }

  // 检查当前tab是否可以前进
  Future<bool> _canGoForward() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          return await currentTab.controller?.canGoForward() ?? false;
        } catch (e) {
          debugPrint('检查前进状态时出错: $e');
          return false;
        }
      }
    }
    return false;
  }

  // 检查当前tab是否可以后退
  Future<bool> _canGoBack() async {
    if (_currentTabIndex < _tabs.length && mounted) {
      final currentTab = _tabs[_currentTabIndex];
      if (!currentTab.isDisposed) {
        try {
          return await currentTab.controller?.canGoBack() ?? false;
        } catch (e) {
          debugPrint('检查后退状态时出错: $e');
          return false;
        }
      }
    }
    return false;
  }

  // 返回主页
  void _goHome() {
    Navigator.of(context).pop();
  }

  // 从URL提取域名用于显示
  String _extractDomainFromUrl(String url) {
    if (url.isEmpty) return '';

    try {
      final uri = Uri.parse(url);
      String host = uri.host;

      // 移除 www. 前缀
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      return host.isNotEmpty ? host : url;
    } catch (e) {
      // 如果解析失败，返回原始URL
      return url;
    }
  }

  // 开始下载
  Future<void> _startDownload(BuildContext context) async {
    if (_currentTabIndex >= _tabs.length) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      final currentTab = _tabs[_currentTabIndex];

      debugPrint('开始下载 - 当前tab: ${currentTab.title}, URL: ${currentTab.url}');
      // 检查基本条件
      if (currentTab.url.isEmpty || currentTab.url == 'about:blank') {
        throw Exception('Please navigate to a webpage first');
      }

      // 检查并等待WebView controller
      if (currentTab.controller == null || currentTab.isDisposed) {
        debugPrint('WebView controller不可用，可能是TabBarView还没创建该tab的WebView');

        // 强制切换到当前tab以触发WebView创建
        if (_tabController.index != _currentTabIndex) {
          debugPrint('强制切换到当前tab以创建WebView');
          _tabController.animateTo(_currentTabIndex);

          // 等待TabBarView切换完成
          await Future.delayed(const Duration(milliseconds: 300));
        }

        // 等待WebView controller初始化（最多3秒）
        bool controllerReady = false;
        for (int i = 0; i < 30; i++) {
          await Future.delayed(const Duration(milliseconds: 100));
          // 重新获取最新的tab状态
          final latestTab = _tabs[_currentTabIndex];
          if (latestTab.controller != null && !latestTab.isDisposed) {
            controllerReady = true;
            debugPrint('WebView controller已准备好');
            break;
          }
        }

        if (!controllerReady) {
          throw Exception('WebView controller not available. The page may need more time to load.');
        }
      }

      // 重新获取最新的tab状态，确保controller是最新的
      final latestTab = _tabs[_currentTabIndex];
      final extractor = ExtractorFactory.createExtractor(
        controller: latestTab.controller!,
        currentUrl: latestTab.url,
      );

      final urls = await extractor.extractImageUrls();
      debugPrint("Browser提取到 ${urls.length} 个图片URL：$urls");

      if (urls.isEmpty) {
        ToastWidget.show(AppLocalizations.of(context)!.noImagesFound);
      } else {
        final needsWebView = extractor.needsWebViewForImages();

        if (needsWebView) {
          debugPrint("检测到需要使用WebView获取图片的网站: ${currentTab.url}");

          final images = await extractor.downloadImages(urls);
          debugPrint("WebView获取到 ${images.length} 张有效图片");

          if (images.isNotEmpty) {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (_) {
                return BrowserDownloadPage(
                  imageUrls: urls.sublist(0, images.length),
                  preloadedImages: images,
                );
              }),
            );
          } else {
            ToastWidget.show(AppLocalizations.of(context)!.unableToProcessImages);
          }
        } else {
          debugPrint("检测到不需要使用WebView获取图片的网站: ${currentTab.url}");
          final String cookies = await extractor.getCookies();
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) {
              return BrowserDownloadPage(
                imageUrls: urls,
                cookies: cookies,
                referer: currentTab.url,
              );
            }),
          );
        }
      }
    } catch (e) {
      debugPrint("Download Error: $e");
      if (mounted) {
        ToastWidget.showTop(AppLocalizations.of(context)!.downloadFailed(e.toString()));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  // 检查当前页面是否可用的基础方法
  bool _isCurrentPageAvailable() {
    if (_currentTabIndex >= _tabs.length) return false;
    
    final currentTab = _tabs[_currentTabIndex];
    
    // 基础检查：URL不为空且不是about:blank，tab未销毁
    final hasValidUrl = currentTab.url.isNotEmpty && currentTab.url != 'about:blank';
    final isNotDisposed = !currentTab.isDisposed;
    
    return hasValidUrl && isNotDisposed;
  }

  // 刷新翻译按钮状态
  void _refreshTranslateButtonState() {
    if (mounted) {
      setState(() {
        // 这个setState会触发build方法重新执行
        // 从而重新评估_isCurrentPageAvailable()的结果
      });
    }
  }

  // 页面加载完成回调
  void _onPageLoadComplete(int tabIndex) {
    debugPrint('MultiBrowserPage: Page load complete for tab $tabIndex, current tab: $_currentTabIndex, translation enabled: $_translationToggleEnabled');

    // Only process if this is the current tab
    if (tabIndex == _currentTabIndex && tabIndex < _tabs.length) {
      final currentTab = _tabs[tabIndex];
      if (currentTab.controller != null) {
        // Delay a bit to ensure page is fully rendered
        Future.delayed(const Duration(milliseconds: 500), () async {
          if (tabIndex == _currentTabIndex && currentTab.controller != null) {
            try {
              // If translation was enabled, re-enable it for the new page
              if (_translationToggleEnabled) {
                debugPrint('MultiBrowserPage: Re-enabling translation for new page');
                await _enableTranslationForCurrentTab();
              }
            } catch (e) {
              debugPrint('MultiBrowserPage: Failed to re-enable translation after page load: $e');
            }
          }
        });
      }
    }
  }

  // 处理缓存清除请求
  Future<void> _handleCacheClear(String imageUrl) async {
    try {
      debugPrint('MultiBrowserPage: Clearing cache for $imageUrl');

      // Clear cache through translation workflow
      await _translationWorkflow.clearImageCache(imageUrl);

      // Also remove any existing overlays for this image
      await _translationWorkflow.removeImageOverlays(imageUrl);

      debugPrint('MultiBrowserPage: Cache cleared successfully for $imageUrl');
    } catch (e) {
      debugPrint('MultiBrowserPage: Failed to clear cache for $imageUrl: $e');
    }
  }

  // 为当前标签页启用翻译（页面加载后恢复翻译状态用）
  Future<void> _enableTranslationForCurrentTab() async {
    try {
      final currentTab = _tabs[_currentTabIndex];

      // 确保WebView controller可用
      if (currentTab.controller == null || currentTab.isDisposed) {
        debugPrint('MultiBrowserPage: WebView controller not available for translation re-enable');
        return;
      }

      // Set WebView controller for translation workflow
      _translationWorkflow.setWebViewController(currentTab.controller);

      // Initialize lazy processing system
      await _translationWorkflow.initializeLazyProcessing();

      // Enable translation mode (show action buttons)
      final localizations = AppLocalizations.of(context)!;
      await _translationWorkflow.setTranslationMode(true, buttonText: localizations.translateThisImage);

      // Re-initialize image observation and check for cached images after a short delay
      Future.delayed(const Duration(milliseconds: 1000), () async {
        if (_translationToggleEnabled && currentTab.controller != null) {
          try {
            // Use service method instead of inline JavaScript
            await _translationWorkflow.reinitializeTranslationSystem();
          } catch (e) {
            debugPrint('MultiBrowserPage: Failed to re-enable translation observation: $e');
          }
        }
      });
    } catch (e) {
      debugPrint('MultiBrowserPage: Failed to re-enable translation: $e');
    }
  }

  // 初始化翻译工作流服务
  Future<void> _initializeServices() async {
    try {
      await _translationWorkflow.initialize();
      _servicesInitialized = true;
      debugPrint('MultiBrowserPage: Translation workflow service initialized successfully');

      // Initialize download notification service
      DownloadNotificationService.instance.setContext(context);
    } catch (e) {
      debugPrint('MultiBrowserPage: Failed to initialize translation workflow service - $e');
      _servicesInitialized = false;
    }
  }

  // 切换本地翻译
  Future<void> _toggleLocalTranslation(BuildContext context) async {
    if (_currentTabIndex >= _tabs.length) return;

    if (!_servicesInitialized) {
      ToastWidget.show(AppLocalizations.of(context)!.loading);
      return;
    }

    if (_translationToggleEnabled) {
      await _disableTranslation();
    } else {
      await _enableTranslation(context);
    }
  }

  // 启用翻译
  Future<void> _enableTranslation(BuildContext context) async {
    setState(() {
      _isLocalTranslating = true;
    });

    try {
      final currentTab = _tabs[_currentTabIndex];

      // 确保WebView controller可用
      bool controllerReady = false;
      int attempts = 0;
      const maxAttempts = 10;

      while (!controllerReady && attempts < maxAttempts) {
        await _ensureCurrentTabWebViewCreated();
        controllerReady = currentTab.controller != null && !currentTab.isDisposed;

        if (!controllerReady) {
          await Future.delayed(const Duration(milliseconds: 500));
          attempts++;
        }
      }

      if (!controllerReady) {
        ToastWidget.show("WebView not ready for translation");
        return;
      }

      // Set WebView controller for translation workflow
      _translationWorkflow.setWebViewController(currentTab.controller);

      // Initialize lazy processing system
      await _translationWorkflow.initializeLazyProcessing();

      // Add JavaScript handler for image visibility
      currentTab.controller!.addJavaScriptHandler(
        handlerName: 'onImageVisible',
        callback: (args) async {
          if (args.isNotEmpty) {
            final imageUrl = args[0] as String;
            debugPrint('MultiBrowserPage: Image became visible: $imageUrl');
            await _processVisibleImage(imageUrl, context);
          }
        },
      );

      // Add JavaScript handler for manual image translation requests
      currentTab.controller!.addJavaScriptHandler(
        handlerName: 'onTranslateImageRequested',
        callback: (args) async {
          if (args.isNotEmpty) {
            final imageUrl = args[0] as String;
            debugPrint('MultiBrowserPage: Manual translation requested for: $imageUrl');

            // Process the image immediately
            await _processImageTranslation(imageUrl, context);
          }
        },
      );

      // Add JavaScript handler for cache clearing requests
      currentTab.controller!.addJavaScriptHandler(
        handlerName: 'onClearImageCache',
        callback: (args) async {
          if (args.isNotEmpty) {
            final imageUrl = args[0] as String;
            debugPrint('MultiBrowserPage: Cache clear requested for: $imageUrl');
            await _handleCacheClear(imageUrl);
          }
        },
      );

      // 注入浏览器增强功能
      await _injectBrowserEnhancements(currentTab.controller!);

      // 先设置翻译开关状态
      setState(() {
        _translationToggleEnabled = true;
      });

      // 清理之前的处理状态，确保干净的开始
      _processingImages.clear();
      _lastProcessTime.clear();
      await _translationWorkflow.clearProcessingStates();

      // Enable translation mode (show action buttons)
      final localizations = AppLocalizations.of(context)!;
      await _translationWorkflow.setTranslationMode(true, buttonText: localizations.translateThisImage);

      // Wait a bit for the page to be ready, then reinitialize image observation
      Future.delayed(const Duration(milliseconds: 1000), () async {
        if (_translationToggleEnabled && currentTab.controller != null) {
          try {
            debugPrint('MultiBrowserPage: Reinitializing translation system after toggle enabled');
            // Use service method instead of inline JavaScript
            await _translationWorkflow.reinitializeTranslationSystem();
          } catch (e) {
            debugPrint('MultiBrowserPage: Failed to re-initialize image observation: $e');
          }
        }
      });

      // ToastWidget.show("Translation enabled - images will be processed as they become visible");

    } catch (e) {
      debugPrint('Local translation error: $e');
      ToastWidget.show('Translation failed: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLocalTranslating = false;
        });
      }
    }
  }

  // 统一的图片翻译处理方法 - 基于手动处理的完整逻辑
  Future<void> _processImageTranslation(String imageUrl, BuildContext context) async {
    if (!_servicesInitialized) {
      debugPrint('MultiBrowserPage: Services not initialized, skipping image processing');
      return;
    }

    try {
      debugPrint('MultiBrowserPage: Processing translation request for: $imageUrl');

      // Update action button state to processing
      await _translationWorkflow.updateActionButtonState(imageUrl, 'processing');

      // Get current tab and extractor
      final currentTab = _tabs[_currentTabIndex];
      if (currentTab.controller == null) {
        await _translationWorkflow.updateActionButtonState(imageUrl, 'ready');
        return;
      }

      // Create extractor for current tab
      final extractor = _createExtractorForCurrentTab();
      if (extractor == null) {
        await _translationWorkflow.updateActionButtonState(imageUrl, 'ready');
        return;
      }

      // Process single image with caching
      final localizations = AppLocalizations.of(context)!;
      final result = await _translationWorkflow.processSingleImage(
        imageUrl: imageUrl,
        extractor: extractor,
        context: context,
        loadingText: localizations.imageTranslationLoading,
        onProgress: (message) {
          debugPrint("Translation progress for $imageUrl: $message");
        },
      );

      if (result.success) {
        // debugPrint('MultiBrowserPage: Successfully processed translation for $imageUrl');
        // debugPrint('MultiBrowserPage: Translation result - ${result.textElementsCount} text elements translated');

        // Debug loading indicators status after success
        await _translationWorkflow.debugLoadingIndicators();

        // Note: Action button state is automatically updated by createImageOverlay
      } else {
        debugPrint('MultiBrowserPage: Failed to process translation for $imageUrl - ${result.message}');

        // Hide loading indicator for this specific image on failure
        await _translationWorkflow.hideLoadingIndicator(imageUrl);

        // Debug loading indicators status after failure
        await _translationWorkflow.debugLoadingIndicators();

        // Reset button state on failure
        await _translationWorkflow.updateActionButtonState(imageUrl, 'ready');
      }
    } catch (e) {
      debugPrint('MultiBrowserPage: Error processing translation for $imageUrl - $e');

      // Hide loading indicator for this specific image on error
      try {
        await _translationWorkflow.hideLoadingIndicator(imageUrl);
        debugPrint('MultiBrowserPage: Loading indicator hidden after error');
      } catch (hideError) {
        debugPrint('MultiBrowserPage: Failed to hide loading indicator - $hideError');
      }

      // Reset button state on error
      await _translationWorkflow.updateActionButtonState(imageUrl, 'ready');
    }
  }

  // 处理可见图片 - 添加防重复处理和缓存检查（修复缓存图片显示问题）
  Future<void> _processVisibleImage(String imageUrl, BuildContext context) async {
    // 检查翻译开关状态
    if (!_translationToggleEnabled) return;

    // 防重复处理检查
    if (_processingImages.contains(imageUrl)) {
      debugPrint('MultiBrowserPage: Image already being processed, skipping: $imageUrl');
      return;
    }

    // 检查最小处理间隔
    final lastProcessTime = _lastProcessTime[imageUrl];
    if (lastProcessTime != null) {
      final timeSinceLastProcess = DateTime.now().difference(lastProcessTime);
      if (timeSinceLastProcess < _minProcessInterval) {
        debugPrint('MultiBrowserPage: Image processed too recently (${timeSinceLastProcess.inMilliseconds}ms ago), skipping: $imageUrl');
        return;
      }
    }

    // 检查缓存 - 如果已缓存，直接显示翻译结果
    final isCached = await _translationWorkflow.isImageCached(imageUrl);
    if (isCached) {
      debugPrint('MultiBrowserPage: Image already cached, displaying cached translation: $imageUrl');

      // 记录处理时间，防止重复调用
      _lastProcessTime[imageUrl] = DateTime.now();

      try {
        // 直接显示缓存的翻译结果
        await _displayCachedTranslation(imageUrl);
      } catch (e) {
        debugPrint('MultiBrowserPage: Failed to display cached translation for $imageUrl: $e');
      }
      return;
    }

    // 记录开始处理
    _processingImages.add(imageUrl);
    _lastProcessTime[imageUrl] = DateTime.now();

    try {
      // 调用统一的处理方法
      await _processImageTranslation(imageUrl, context);
    } finally {
      // 确保处理完成后移除标记
      _processingImages.remove(imageUrl);
    }
  }

  // 创建当前标签页的提取器
  dynamic _createExtractorForCurrentTab() {
    final currentTab = _tabs[_currentTabIndex];
    if (currentTab.controller == null) return null;

    return ExtractorFactory.createExtractor(
      controller: currentTab.controller!,
      currentUrl: currentTab.url,
    );
  }

  // 禁用翻译
  Future<void> _disableTranslation({bool skipUI = false}) async {
    try {
      await _translationWorkflow.cleanup();

      // Remove JavaScript handlers
      if (_currentTabIndex < _tabs.length) {
        final currentTab = _tabs[_currentTabIndex];
        if (currentTab.controller != null) {
          try {
            await currentTab.controller!.removeJavaScriptHandler(handlerName: 'onImageVisible');
          } catch (e) {
            debugPrint('Error removing JavaScript handler: $e');
          }
        }
      }

      _translationToggleEnabled = false;

      // Disable translation mode (hide action buttons)
      await _translationWorkflow.setTranslationMode(false);

      if (!skipUI && mounted) {
        setState(() {
          // UI update only if not skipping UI and widget is still mounted
        });
      }
    } catch (e) {
      debugPrint('Error disabling translation: $e');
    }
  }

  // 清理翻译状态（用于标签页切换和页面导航）
  void _cleanupTranslationState() {
    if (_translationToggleEnabled) {
      _translationWorkflow.cleanup().catchError((e) {
        debugPrint('Error cleaning up translation state: $e');
      });
      _translationToggleEnabled = false;
    }

    // 清理防重复处理状态
    _processingImages.clear();
    _lastProcessTime.clear();

    // 清理JavaScript端的处理状态
    _translationWorkflow.clearProcessingStates().catchError((e) {
      debugPrint('Error clearing JavaScript processing states: $e');
    });
  }

  // 确保当前tab的WebView被创建
  Future<void> _ensureCurrentTabWebViewCreated() async {
    if (_currentTabIndex >= _tabs.length) return;
    final currentTab = _tabs[_currentTabIndex];

    // 如果controller已存在，直接返回
    if (currentTab.controller != null && !currentTab.isDisposed) {
      return;
    }

    // 确保TabBarView切换到当前tab
    if (_tabController.index != _currentTabIndex) {
      _tabController.animateTo(_currentTabIndex);
      await Future.delayed(const Duration(milliseconds: 300));
    }

    // 等待WebView创建
    for (int i = 0; i < 30; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      final latestTab = _tabs[_currentTabIndex];
      if (latestTab.controller != null && !latestTab.isDisposed) {
        debugPrint('WebView创建成功');
        return;
      }
    }
    debugPrint('WebView创建超时');
  }

  // 注入浏览器增强功能
  Future<void> _injectBrowserEnhancements(InAppWebViewController controller) async {
    try {
      // 等待 WebView 准备就绪
      final isReady = await _injectionService.waitForWebViewReady(controller);
      if (!isReady) {
        debugPrint('WebView not ready for enhancement injection');
        return;
      }

      // 按依赖顺序加载资源
      final loadedResources = await WebViewJsResources.loadResourcesWithDependencies(
        ['browser_utils', 'ad_block', 'content_extractor'],
        (script) async {
          await _injectionService.injectJavaScript(controller, script);
        },
      );

      debugPrint('Browser enhancements loaded: $loadedResources');

      // 初始化广告拦截
      await controller.evaluateJavascript(source: 'window.adBlocker.init();');
      
      debugPrint('Browser enhancements injected successfully');
    } catch (e) {
      debugPrint('Failed to inject browser enhancements: $e');
    }
  }

  // 书签管理功能 - 切换书签状态（添加或移除）
  Future<void> _toggleBookmark() async {
    if (!_canBookmarkCurrentPage()) return;
    final currentTab = _tabs[_currentTabIndex];

    try {
      final isAdded = await BookmarkService.instance.toggleBookmark(
        currentTab.url,
        currentTab.title.isNotEmpty ? currentTab.title : currentTab.url,
        favicon: null, // 可以在这里添加favicon逻辑
      );

      if (isAdded) {
        ToastWidget.show(AppLocalizations.of(context)!.bookmarkAdded);
        debugPrint('书签已添加: ${currentTab.title} - ${currentTab.url}');
      } else {
        ToastWidget.show(AppLocalizations.of(context)!.bookmarkDeleted);
        debugPrint('书签已移除: ${currentTab.title} - ${currentTab.url}');
      }

      setState(() {});
    } catch (e) {
      debugPrint('书签操作失败: $e');
      ToastWidget.show(AppLocalizations.of(context)!.bookmarkFailed);
    }
  }

  // 检查当前页面是否已收藏
  Future<bool> _isCurrentPageBookmarked() async {
    if (!_canBookmarkCurrentPage()) return false;
    final currentTab = _tabs[_currentTabIndex];
    try {
      return await BookmarkService.instance.isBookmarked(currentTab.url);
    } catch (e) {
      debugPrint('检查书签状态失败: $e');
      return false;
    }
  }

  // 检查当前页面是否可以被收藏
  bool _canBookmarkCurrentPage() {
    // 书签需要基础检查 + 额外的协议检查
    if (!_isCurrentPageAvailable()) return false;
    
    final currentTab = _tabs[_currentTabIndex];
    
    // 检查是否为空白tab
    if (currentTab.isEmpty) return false;
    
    // 检查是否为有效的HTTP/HTTPS URL
    try {
      final uri = Uri.parse(currentTab.url);
      return uri.scheme == 'http' || uri.scheme == 'https';
    } catch (e) {
      return false;
    }
  }

  // 显示更多菜单
  void _showMoreMenu() {
    setState(() {
      _isMoreMenuVisible = true;
    });

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return _buildMoreMenu();
      },
    ).then((_) {
      setState(() {
        _isMoreMenuVisible = false;
      });
    });
  }

  // 构建更多菜单
  Widget _buildMoreMenu() {
    final themeColors = ThemeManager.currentTheme;

    return Container(
      decoration: BoxDecoration(
        color: themeColors.backgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              decoration: BoxDecoration(
                color: themeColors.textColor.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 菜单项
            _buildMoreMenuItem(
              icon: Icons.add,
              title: AppLocalizations.of(context)!.newTab,
              onTap: () {
                Navigator.pop(context);
                _createNewTab();
              },
            ),

            FutureBuilder<bool>(
              future: _isCurrentPageBookmarked(),
              builder: (context, snapshot) {
                final isBookmarked = snapshot.data ?? false;
                final canBookmark = _canBookmarkCurrentPage();
                return _buildMoreMenuItem(
                  icon: isBookmarked ? 'icon_favorite_on' : 'icon_favorite_off',
                  title: isBookmarked
                    ? AppLocalizations.of(context)!.removeBookmark
                    : AppLocalizations.of(context)!.addBookmark,
                  onTap: canBookmark ? () {
                    Navigator.pop(context);
                    _toggleBookmark();
                  } : null,
                );
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 构建更多菜单项
  Widget _buildMoreMenuItem({
    required dynamic icon,
    required String title,
    VoidCallback? onTap,
  }) {
    final themeColors = ThemeManager.currentTheme;
    final isEnabled = onTap != null;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            if (icon is IconData)
              Icon(
                icon,
                size: 24,
                color: isEnabled
                  ? themeColors.textColor
                  : themeColors.textColor.withValues(alpha: 0.3),
              )
            else if (icon is String)
              SvgPicture.asset(
                ThemeManager.getImagePath(icon),
                width: 22,
                height: 22,
              ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: isEnabled
                  ? themeColors.textColor
                  : themeColors.textColor.withValues(alpha: 0.3),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeColors = ThemeManager.currentTheme;

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // 用户通过返回按钮离开浏览器，清理翻译状态
          if (_translationToggleEnabled) {
            _disableTranslation(skipUI: true).catchError((e) {
              debugPrint('Error cleaning up translation on pop: $e');
            });
          }
        }
      },
      child: Scaffold(
        backgroundColor: themeColors.backgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              // WebView内容区域 - 现在占据顶部到底部工具栏之间的全部空间
              Expanded(
                child: _tabs.isEmpty
                    ? const Center(child: Text('No tabs available'))
                    : TabBarView(
                        controller: _tabController,
                        physics: const NeverScrollableScrollPhysics(), // 禁用左右滑动切换tab
                        children: _tabs.asMap().entries.map((entry) {
                          final index = entry.key;
                          final tab = entry.value;

                          // 智能处理initialInput
                          String? initialInput;
                          if (index == 0 && widget.initialUrl != null && widget.initialUrl!.isNotEmpty) {
                            // 新的初始URL（优先级最高）
                            initialInput = widget.initialUrl;
                            debugPrint('Tab $index: 使用新的初始URL: $initialInput');
                          } else if (tab.url.isNotEmpty) {
                            // 恢复的tab或有URL的tab，传递保存的URL
                            initialInput = tab.url;
                            debugPrint('Tab $index: 使用恢复的URL: $initialInput');
                          } else {
                            debugPrint('Tab $index: 无URL，将显示推荐站点');
                          }

                          return BrowserTabView(
                            key: _tabKeys[tab.id],
                            tab: tab,
                            onTabUpdated: _updateTab,
                            initialInput: initialInput,
                            onPageLoadComplete: () => _onPageLoadComplete(index),
                          );
                        }).toList(),
                      ),
              ),

              // 底部工具栏 - 两行布局，类似iPhone Safari
              Container(
                decoration: BoxDecoration(
                  color: themeColors.backgroundColor,
                  border: Border(
                    top: BorderSide(
                      color: themeColors.textColor.withValues(alpha: 0.2),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 第一行：水平滚动的地址栏
                    Container(
                      height: 48,
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                      alignment: Alignment.center,
                      child: ListView.builder(
                        controller: _tabScrollController,
                        scrollDirection: Axis.horizontal,
                        itemCount: _tabs.length,
                        itemBuilder: (context, index) {
                          final tab = _tabs[index];
                          final isCurrentTab = index == _currentTabIndex;
                          final isEditing = _editingTabId == tab.id;
                          final controller = _addressControllers[tab.id];
                          final focusNode = _addressFocusNodes[tab.id];

                          if (controller == null || focusNode == null) {
                            return const SizedBox.shrink();
                          }

                          // 如果不在编辑状态，显示域名；编辑状态显示完整URL
                          if (!isEditing) {
                            final displayText = tab.url.isEmpty ? '' : _extractDomainFromUrl(tab.url);
                            // 只在内容真正改变时才更新controller
                            if (controller.text != displayText) {
                              controller.text = displayText;
                            }
                          }

                          return Container(
                            width: MediaQuery.of(context).size.width * 0.8,
                            margin: EdgeInsets.only(
                              left: index == 0 ? MediaQuery.of(context).size.width * 0.05 : 0, // 第一个tab左侧增加空白
                              right: index == _tabs.length - 1
                                  ? MediaQuery.of(context).size.width * 0.05 + 8
                                  : 8, // 最后一个tab右侧增加空白，其他tab保持8px间距
                            ),
                            decoration: BoxDecoration(
                              color: isCurrentTab
                                  ? themeColors.inputBoxBgColor
                                  : themeColors.inputBoxBgColor.withValues(alpha: 0.5),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color:
                                    isCurrentTab ? themeColors.textColor : themeColors.textColor.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // 关闭按钮（仅在当前tab显示）
                                if (isCurrentTab)
                                  GestureDetector(
                                    onTap: () => _closeTab(index),
                                    child: Container(
                                      width: 28,
                                      height: 28,
                                      margin: const EdgeInsets.only(left: 8, right: 4),
                                      decoration: BoxDecoration(
                                        color: themeColors.textColor.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(14),
                                      ),
                                      child: Icon(
                                        Icons.close,
                                        size: 18,
                                        color: themeColors.textColor.withValues(alpha: 0.7),
                                      ),
                                    ),
                                  ),
                                // 为非当前tab添加左侧间距以保持对齐
                                if (!isCurrentTab) const SizedBox(width: 12),
                                // 加载指示器
                                if (tab.isLoading)
                                  Container(
                                    width: 18,
                                    height: 18,
                                    margin: const EdgeInsets.only(right: 8),
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: themeColors.textColor,
                                    ),
                                  ),
                                // 地址栏输入框
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () {
                                      if (isCurrentTab) {
                                        // 如果是当前tab，直接聚焦到地址栏进行编辑
                                        focusNode.requestFocus();
                                      } else {
                                        // 如果不是当前tab，先切换到该tab
                                        _switchToTab(index);
                                      }
                                    },
                                    child: Theme(
                                      data: Theme.of(context).copyWith(
                                        textSelectionTheme: TextSelectionThemeData(
                                          cursorColor: themeColors.textColor,
                                          selectionColor: themeColors.textColor.withValues(alpha: 0.3),
                                          selectionHandleColor: themeColors.textColor,
                                        ),
                                      ),
                                      child: TextField(
                                        controller: controller,
                                        focusNode: focusNode,
                                        keyboardType: TextInputType.url,
                                        textInputAction: TextInputAction.go,
                                        cursorColor: themeColors.textColor,
                                        textAlign: isCurrentTab && isEditing ? TextAlign.start :  tab.url.isEmpty ? TextAlign.start : TextAlign.center,
                                        decoration: InputDecoration(
                                          border: InputBorder.none,
                                          hintText: (isEditing || tab.url.isEmpty) ? AppLocalizations.of(context)!.enterUrl : "",
                                          hintStyle: TextStyle(fontSize: 14, color: Colors.grey.shade400),
                                          contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 2),
                                          isDense: true,
                                        ),
                                        style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            color: isCurrentTab
                                                ? themeColors.textColor
                                                : themeColors.textColor.withValues(alpha: 0.7)),
                                        onSubmitted: (value) => _loadUrlFromAddressBar(tab.id),
                                        textAlignVertical: TextAlignVertical.center,
                                        enabled: isCurrentTab, // 只有当前tab可以编辑
                                      ),
                                    ),
                                  ),
                                ),
                                // 搜索/停止/刷新按钮（仅在当前tab显示）
                                if (isCurrentTab)
                                  Padding(
                                    padding: const EdgeInsets.only(left: 4.0, right: 8.0),
                                    child: GestureDetector(
                                      onTap: () {
                                        if (tab.isLoading) {
                                          _stopLoading();
                                        } else if (_shouldShowSearchIcon(tab.id)) {
                                          _loadUrlFromAddressBar(tab.id);
                                        } else {
                                          // 刷新页面
                                          _refreshTab(tab.id);
                                        }
                                      },
                                      child: tab.isLoading
                                          ? Icon(Icons.close, size: 22, color: themeColors.textColor)
                                          : _shouldShowSearchIcon(tab.id)
                                              ? Image.asset(
                                                  ThemeManager.getImagePath('icon_search'),
                                                  width: 22,
                                                  height: 22,
                                                  color: themeColors.textColor,
                                                )
                                              : Icon(Icons.refresh, size: 22, color: themeColors.textColor),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                    // 第二行：导航控制按钮
                    Container(
                      height: 50,
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      child: Row(
                        children: [
                          // 左侧：主页、后退、前进和下载按钮
                          Row(
                            children: [
                              ButtonWidget.createSvgButton(ThemeManager.getImagePath('icon_home'), 24, 24, onPressed: _goHome),
                              const SizedBox(width: 8),
                              IconButton(
                                icon: Icon(Icons.arrow_back_ios,
                                    color: _canGoBackState
                                        ? themeColors.textColor
                                        : themeColors.textColor.withValues(alpha: 0.3),
                                    size: 24),
                                onPressed: _canGoBackState ? _goBack : null,
                                padding: EdgeInsets.zero,
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                icon: Icon(Icons.arrow_forward_ios,
                                    color: _canGoForwardState
                                        ? themeColors.textColor
                                        : themeColors.textColor.withValues(alpha: 0.3),
                                    size: 24),
                                onPressed: _canGoForwardState ? _goForward : null,
                                padding: EdgeInsets.zero,
                              ),
                              const SizedBox(width: 8),
                              // 下载按钮 - 移到左侧导航控件区域
                              IconButton(
                                icon: _isDownloading
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: Colors.black87,
                                        ),
                                      )
                                    : Image.asset(
                                        ThemeManager.getImagePath('icon_download'),
                                        width: 24,
                                        height: 24,
                                        color: _isCurrentPageAvailable() ? themeColors.textColor : themeColors.textColor.withValues(alpha: 0.3),
                              ),
                            onPressed: _isCurrentPageAvailable() && !_isDownloading
                                    ? () => _startDownload(context)
                                    : null,
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),

                          // 中间：翻译滑块控件（使用Expanded来居中）
                          Expanded(
                            child: Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // 翻译滑块/开关控件
                                  Container(
                                    width: 100,
                                    height: 38,
                                    decoration: BoxDecoration(
                                      color: _translationToggleEnabled
                                        ? themeColors.logoColor.withValues(alpha: 0.2) // Light green background when enabled
                                        : Colors.grey[200],
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: _isLocalTranslating
                                        ? const Center(
                                            child: SizedBox(
                                              width: 22,
                                              height: 22,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                                color: Colors.black87,
                                              ),
                                            ),
                                          )
                                        : GestureDetector(
                                            onTap: (_isCurrentPageAvailable() && _servicesInitialized)
                                                ? () => _toggleLocalTranslation(context)
                                                : () {
                                                  ToastWidget.show(
                                                    AppLocalizations.of(context)!.loading
                                                  );
                                                  },
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(
                                                    ThemeManager.getImagePath('icon_translate'),
                                                    width: 20,
                                                    height: 20,
                                                    colorFilter: ColorFilter.mode(
                                                      (_isCurrentPageAvailable() && _servicesInitialized) ? Colors.black87 : Colors.grey[600]!,
                                                      BlendMode.srcIn,
                                                    ),
                                                  ),
                                                const SizedBox(width: 6),
                                                // Toggle switch
                                                Container(
                                                  width: 40,
                                                  height: 20,
                                                  decoration: BoxDecoration(
                                                    color: _translationToggleEnabled
                                                      ? themeColors.logoColor
                                                      : Colors.grey[400],
                                                    borderRadius: BorderRadius.circular(10),
                                                  ),
                                                  child: AnimatedAlign(
                                                    duration: const Duration(milliseconds: 200),
                                                    alignment: _translationToggleEnabled
                                                      ? Alignment.centerRight
                                                      : Alignment.centerLeft,
                                                    child: Container(
                                                      width: 16,
                                                      height: 16,
                                                      margin: const EdgeInsets.all(2),
                                                      decoration: const BoxDecoration(
                                                        color: Colors.white,
                                                        shape: BoxShape.circle,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // 右侧：更多菜单按钮
                          ButtonWidget.createIconButton(Icons.more_vert, 26, onPressed: _showMoreMenu),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示缓存的翻译结果
  Future<void> _displayCachedTranslation(String imageUrl) async {
    try {
      debugPrint('MultiBrowserPage: Loading cached translation for: $imageUrl');

      // 从缓存加载翻译结果并显示
      final result = await _translationWorkflow.loadAndDisplayCachedTranslation(imageUrl);

      if (result != null) {
        debugPrint('MultiBrowserPage: Successfully displayed cached translation for $imageUrl');
        debugPrint('MultiBrowserPage: Cached translation - ${result.textElementsCount} text elements');
      } else {
        debugPrint('MultiBrowserPage: No cached translation found for $imageUrl');
      }
    } catch (e) {
      debugPrint('MultiBrowserPage: Error displaying cached translation for $imageUrl: $e');
    }
  }

  /// 调试方法：检查处理状态
  void _debugProcessingState() {
    debugPrint('MultiBrowserPage: Current processing state:');
    debugPrint('  - Translation enabled: $_translationToggleEnabled');
    debugPrint('  - Services initialized: $_servicesInitialized');
    debugPrint('  - Processing images: ${_processingImages.length}');
    debugPrint('  - Last process times: ${_lastProcessTime.length}');

    if (_processingImages.isNotEmpty) {
      debugPrint('  - Currently processing: ${_processingImages.toList()}');
    }
  }
}
